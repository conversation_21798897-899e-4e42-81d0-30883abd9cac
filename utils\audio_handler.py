"""
معالج الصوت لتطبيق EyeGuard AI
Audio Handler for EyeGuard AI - Text to Speech functionality
"""

import pyttsx3
import threading
import time

class AudioHandler:
    """
    فئة التعامل مع الصوت وتحويل النص لكلام
    """
    
    def __init__(self):
        self.engine = None
        self.is_speaking = False
        self.init_engine()
    
    def init_engine(self):
        """
        تهيئة محرك تحويل النص لصوت
        """
        try:
            self.engine = pyttsx3.init()
            
            # إعداد خصائص الصوت
            voices = self.engine.getProperty('voices')
            
            # البحث عن صوت عربي أو أنثوي
            for voice in voices:
                if 'arabic' in voice.name.lower() or 'female' in voice.name.lower():
                    self.engine.setProperty('voice', voice.id)
                    break
            
            # إعداد سرعة الكلام
            self.engine.setProperty('rate', 150)  # كلمة في الدقيقة
            
            # إعداد مستوى الصوت
            self.engine.setProperty('volume', 0.8)  # من 0.0 إلى 1.0
            
        except Exception as e:
            print(f"خطأ في تهيئة محرك الصوت: {e}")
            self.engine = None
    
    def speak_text(self, text, async_mode=True):
        """
        تحويل النص إلى كلام
        """
        if not self.engine:
            print("محرك الصوت غير متاح")
            return False
        
        if self.is_speaking:
            self.stop_speaking()
        
        if async_mode:
            # تشغيل الصوت في خيط منفصل
            thread = threading.Thread(target=self._speak_sync, args=(text,))
            thread.daemon = True
            thread.start()
        else:
            self._speak_sync(text)
        
        return True
    
    def _speak_sync(self, text):
        """
        تحويل النص إلى كلام بشكل متزامن
        """
        try:
            self.is_speaking = True
            self.engine.say(text)
            self.engine.runAndWait()
        except Exception as e:
            print(f"خطأ في تحويل النص لصوت: {e}")
        finally:
            self.is_speaking = False
    
    def stop_speaking(self):
        """
        إيقاف الكلام
        """
        if self.engine and self.is_speaking:
            try:
                self.engine.stop()
                self.is_speaking = False
            except Exception as e:
                print(f"خطأ في إيقاف الصوت: {e}")
    
    def speak_diagnosis(self, prediction_result):
        """
        قراءة نتيجة التشخيص صوتياً
        """
        class_name = prediction_result['class_name']
        confidence = prediction_result['confidence']
        
        # تحضير النص للقراءة
        if class_name == 'طبيعي':
            text = f"الحمد لله، العين تبدو طبيعية. مستوى الثقة {confidence:.0%}"
        elif class_name == 'احمرار والتهاب':
            text = f"يوجد احمرار أو التهاب في العين. مستوى الثقة {confidence:.0%}. ينصح بمراجعة الطبيب"
        elif class_name == 'مياه بيضاء':
            text = f"يشتبه في وجود مياه بيضاء. مستوى الثقة {confidence:.0%}. يجب مراجعة طبيب العيون فوراً"
        elif class_name == 'مشاكل الشبكية':
            text = f"يوجد مشاكل محتملة في الشبكية. مستوى الثقة {confidence:.0%}. مراجعة طبيب العيون ضرورية"
        elif class_name == 'جفاف العين':
            text = f"يبدو أن هناك جفاف في العين. مستوى الثقة {confidence:.0%}. استخدم قطرات مرطبة"
        else:
            text = f"تم اكتشاف حالة: {class_name}. مستوى الثقة {confidence:.0%}"
        
        return self.speak_text(text)
    
    def speak_welcome(self):
        """
        رسالة ترحيب صوتية
        """
        welcome_text = "أهلاً بك في تطبيق آي جارد للذكاء الاصطناعي. اختر صورة العين للتحليل"
        return self.speak_text(welcome_text)
    
    def speak_instructions(self):
        """
        تعليمات الاستخدام صوتياً
        """
        instructions = """
        لاستخدام التطبيق:
        أولاً: اضغط على زر اختيار صورة
        ثانياً: اختر صورة واضحة للعين
        ثالثاً: اضغط على زر تحليل الصورة
        رابعاً: انتظر النتيجة
        تذكر: هذا التطبيق للاستخدام التعليمي فقط
        """
        return self.speak_text(instructions)
    
    def test_audio(self):
        """
        اختبار عمل الصوت
        """
        test_text = "اختبار الصوت. إذا كنت تسمع هذه الرسالة، فالصوت يعمل بشكل صحيح"
        return self.speak_text(test_text, async_mode=False)
    
    def get_available_voices(self):
        """
        الحصول على قائمة الأصوات المتاحة
        """
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'language': getattr(voice, 'languages', ['Unknown']),
                    'gender': getattr(voice, 'gender', 'Unknown')
                }
                voice_list.append(voice_info)
            
            return voice_list
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الأصوات: {e}")
            return []
    
    def set_voice(self, voice_id):
        """
        تغيير الصوت المستخدم
        """
        if not self.engine:
            return False
        
        try:
            self.engine.setProperty('voice', voice_id)
            return True
        except Exception as e:
            print(f"خطأ في تغيير الصوت: {e}")
            return False
    
    def set_speech_rate(self, rate):
        """
        تغيير سرعة الكلام
        """
        if not self.engine:
            return False
        
        try:
            # التأكد من أن السرعة في النطاق المقبول
            rate = max(50, min(300, rate))
            self.engine.setProperty('rate', rate)
            return True
        except Exception as e:
            print(f"خطأ في تغيير سرعة الكلام: {e}")
            return False
    
    def set_volume(self, volume):
        """
        تغيير مستوى الصوت
        """
        if not self.engine:
            return False
        
        try:
            # التأكد من أن مستوى الصوت بين 0.0 و 1.0
            volume = max(0.0, min(1.0, volume))
            self.engine.setProperty('volume', volume)
            return True
        except Exception as e:
            print(f"خطأ في تغيير مستوى الصوت: {e}")
            return False

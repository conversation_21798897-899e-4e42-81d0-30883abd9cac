@echo off
chcp 65001 >nul
title EyeGuard AI - حماية عيونك بالذكاء الاصطناعي

echo.
echo ========================================
echo 👁️  EyeGuard AI
echo حماية عيونك بالذكاء الاصطناعي
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    echo يرجى تثبيت pip أو إعادة تثبيت Python
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ ملف المتطلبات موجود

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المكتبات المطلوبة...
echo.
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت المكتبات
    echo يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح

REM تشغيل التطبيق
echo.
echo 🚀 تشغيل EyeGuard AI...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo يرجى التحقق من رسائل الخطأ أعلاه
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق التطبيق
pause

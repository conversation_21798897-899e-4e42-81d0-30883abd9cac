@echo off
chcp 65001 >nul
title EyeGuard AI - اختبار التطبيق

echo.
echo ========================================
echo 🧪 EyeGuard AI - اختبار التطبيق
echo ========================================
echo.

REM تشغيل اختبارات المكونات
echo 🔍 اختبار مكونات التطبيق...
python main.py --test

if errorlevel 1 (
    echo.
    echo ❌ فشل في اختبار المكونات
    pause
    exit /b 1
)

echo.
echo ✅ نجح اختبار المكونات

REM تشغيل اختبارات الوحدة
echo.
echo 🧪 تشغيل اختبارات الوحدة...
python -m pytest tests/ -v

if errorlevel 1 (
    echo.
    echo ⚠️ بعض الاختبارات فشلت أو pytest غير مثبت
    echo تشغيل الاختبارات البديلة...
    python tests/test_classifier.py
)

echo.
echo 🏁 انتهى الاختبار
pause

"""
مساعد الحافظة لتطبيق EyeGuard AI
Clipboard helper for EyeGuard AI application
"""

import tkinter as tk
from datetime import datetime
import os

class ClipboardHelper:
    """
    فئة مساعدة للتعامل مع الحافظة
    """
    
    def __init__(self, root_window=None):
        self.root = root_window
    
    def copy_to_clipboard(self, text, show_message=True):
        """
        نسخ النص إلى الحافظة
        """
        try:
            if self.root:
                self.root.clipboard_clear()
                self.root.clipboard_append(text)
                self.root.update()
            else:
                # إنشاء نافذة مؤقتة للنسخ
                temp_root = tk.Tk()
                temp_root.withdraw()
                temp_root.clipboard_clear()
                temp_root.clipboard_append(text)
                temp_root.update()
                temp_root.destroy()
            
            if show_message and self.root:
                from tkinter import messagebox
                messagebox.showinfo("نجح", "تم نسخ النتيجة إلى الحافظة بنجاح!")
            
            return True
            
        except Exception as e:
            if show_message and self.root:
                from tkinter import messagebox
                messagebox.showerror("خطأ", f"فشل في نسخ النتيجة: {e}")
            return False
    
    def format_analysis_result(self, prediction_result, image_path=None, include_header=True):
        """
        تنسيق نتيجة التحليل للنسخ
        """
        class_name = prediction_result.get('class_name', 'غير محدد')
        confidence = prediction_result.get('confidence', 0.0)
        
        # إنشاء النص المنسق
        formatted_text = ""
        
        if include_header:
            formatted_text += "👁️ EyeGuard AI - نتيجة تحليل العين\n"
            formatted_text += "=" * 50 + "\n\n"
        
        # معلومات التحليل
        formatted_text += f"📅 تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if image_path:
            formatted_text += f"📁 اسم الملف: {os.path.basename(image_path)}\n"
        
        formatted_text += "\n"
        
        # النتيجة الرئيسية
        formatted_text += "🎯 نتيجة التحليل:\n"
        formatted_text += f"   التشخيص: {class_name}\n"
        formatted_text += f"   مستوى الثقة: {confidence:.1%}\n\n"
        
        # التوصيات بناءً على النتيجة
        recommendations = self.get_recommendations_for_class(class_name)
        if recommendations:
            formatted_text += "💡 التوصيات:\n"
            for rec in recommendations:
                formatted_text += f"   {rec}\n"
            formatted_text += "\n"
        
        # معلومات الإلحاح
        urgency = self.get_urgency_for_class(class_name)
        formatted_text += f"⚠️ مستوى الإلحاح: {urgency}\n\n"
        
        # تنبيه طبي
        formatted_text += "🏥 تنبيه طبي مهم:\n"
        formatted_text += "هذا التطبيق مخصص للاستخدام التعليمي والتوعوي فقط.\n"
        formatted_text += "النتائج المعروضة لا تغني عن استشارة طبيب مختص.\n"
        formatted_text += "في حالة وجود أي مشاكل في العين، يُنصح بزيارة طبيب العيون فوراً.\n\n"
        
        # معلومات التطبيق
        formatted_text += "📱 تم إنشاؤه بواسطة: EyeGuard AI\n"
        formatted_text += "🌐 مفتوح المصدر - للاستخدام التعليمي\n"
        
        return formatted_text
    
    def get_recommendations_for_class(self, class_name):
        """
        الحصول على التوصيات لكل فئة
        """
        recommendations = {
            'طبيعي': [
                '✅ حافظ على نظافة العين',
                '✅ استخدم نظارات شمسية عند التعرض للشمس',
                '✅ تناول الأطعمة الغنية بفيتامين A',
                '✅ خذ فترات راحة عند استخدام الشاشات'
            ],
            'احمرار والتهاب': [
                '🧼 اغسل يديك قبل لمس العين',
                '💧 استخدم كمادات باردة لمدة 10-15 دقيقة',
                '💊 تجنب فرك العين',
                '🚫 تجنب استخدام العدسات اللاصقة'
            ],
            'مياه بيضاء': [
                '👨‍⚕️ راجع طبيب العيون فوراً للتشخيص الدقيق',
                '🕶️ استخدم نظارات شمسية لحماية العين',
                '💡 تجنب القيادة في الليل إذا كانت الرؤية ضبابية',
                '🚭 تجنب التدخين'
            ],
            'مشاكل الشبكية': [
                '🚨 راجع طبيب العيون المختص فوراً',
                '📱 تجنب الاستخدام المفرط للشاشات',
                '🍇 تناول الأطعمة الغنية بالأنثوسيانين',
                '🩺 افحص ضغط الدم بانتظام'
            ],
            'جفاف العين': [
                '💧 استخدم قطرات العين المرطبة بانتظام',
                '💨 تجنب التعرض المباشر للمراوح أو المكيفات',
                '💻 اتبع قاعدة 20-20-20',
                '💧 اشرب كمية كافية من الماء'
            ]
        }
        
        return recommendations.get(class_name, [])
    
    def get_urgency_for_class(self, class_name):
        """
        الحصول على مستوى الإلحاح لكل فئة
        """
        urgency_levels = {
            'طبيعي': 'منخفض',
            'احمرار والتهاب': 'متوسط',
            'مياه بيضاء': 'عالي',
            'مشاكل الشبكية': 'عالي جداً',
            'جفاف العين': 'منخفض إلى متوسط'
        }
        
        return urgency_levels.get(class_name, 'متوسط')
    
    def copy_analysis_result(self, prediction_result, image_path=None, root_window=None, show_message=True):
        """
        نسخ نتيجة التحليل المنسقة إلى الحافظة
        """
        if root_window:
            self.root = root_window
        
        formatted_text = self.format_analysis_result(prediction_result, image_path)
        return self.copy_to_clipboard(formatted_text, show_message)
    
    def create_shareable_text(self, prediction_result, image_path=None):
        """
        إنشاء نص قابل للمشاركة (مختصر)
        """
        class_name = prediction_result.get('class_name', 'غير محدد')
        confidence = prediction_result.get('confidence', 0.0)
        
        shareable_text = f"👁️ EyeGuard AI - تحليل العين\n\n"
        shareable_text += f"النتيجة: {class_name}\n"
        shareable_text += f"الثقة: {confidence:.1%}\n"
        shareable_text += f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}\n\n"
        shareable_text += "⚠️ للاستخدام التعليمي فقط - استشر طبيب مختص"
        
        return shareable_text
    
    def copy_shareable_result(self, prediction_result, image_path=None, root_window=None):
        """
        نسخ نتيجة مختصرة قابلة للمشاركة
        """
        if root_window:
            self.root = root_window
        
        shareable_text = self.create_shareable_text(prediction_result, image_path)
        return self.copy_to_clipboard(shareable_text, show_message=True)

# دوال مساعدة للاستخدام المباشر
def copy_text_to_clipboard(text, root_window=None):
    """
    دالة مساعدة لنسخ نص إلى الحافظة
    """
    helper = ClipboardHelper(root_window)
    return helper.copy_to_clipboard(text)

def copy_analysis_to_clipboard(prediction_result, image_path=None, root_window=None):
    """
    دالة مساعدة لنسخ نتيجة التحليل
    """
    helper = ClipboardHelper(root_window)
    return helper.copy_analysis_result(prediction_result, image_path, root_window)

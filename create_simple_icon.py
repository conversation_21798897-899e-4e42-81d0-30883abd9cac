#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة مبسطة لتطبيق EyeGuard AI
Create simple icon for EyeGuard AI application
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_main_icon():
    """
    إنشاء الأيقونة الرئيسية
    """
    # إنشاء مجلد الأيقونات
    icons_dir = "assets/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    print("🎨 إنشاء أيقونة EyeGuard AI...")
    
    # إنشاء أيقونة بحجم 256x256
    size = 256
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # الألوان
    primary_color = (33, 150, 243)      # أزرق EyeGuard
    secondary_color = (76, 175, 80)     # أخضر للصحة
    accent_color = (255, 193, 7)        # ذهبي للذكاء الاصطناعي
    white = (255, 255, 255)
    dark_blue = (13, 71, 161)
    
    center = size // 2
    
    # رسم الخلفية الدائرية الرئيسية
    bg_radius = size // 2 - 8
    draw.ellipse([8, 8, size-8, size-8], fill=primary_color)
    
    # رسم حلقة داخلية للتأثير
    inner_ring = size // 2 - size // 6
    draw.ellipse([
        center - inner_ring, center - inner_ring,
        center + inner_ring, center + inner_ring
    ], outline=white, width=4)
    
    # رسم العين الخارجية (الصلبة)
    eye_radius = size // 4
    eye_bbox = [
        center - eye_radius, center - eye_radius,
        center + eye_radius, center + eye_radius
    ]
    draw.ellipse(eye_bbox, fill=white, outline=dark_blue, width=3)
    
    # رسم القزحية
    iris_radius = size // 5
    iris_bbox = [
        center - iris_radius, center - iris_radius,
        center + iris_radius, center + iris_radius
    ]
    draw.ellipse(iris_bbox, fill=secondary_color, outline=dark_blue, width=2)
    
    # رسم البؤبؤ
    pupil_radius = size // 8
    pupil_bbox = [
        center - pupil_radius, center - pupil_radius,
        center + pupil_radius, center + pupil_radius
    ]
    draw.ellipse(pupil_bbox, fill=dark_blue)
    
    # إضافة نقطة ضوء في البؤبؤ
    light_size = size // 24
    light_x = center - pupil_radius // 3
    light_y = center - pupil_radius // 3
    draw.ellipse([
        light_x - light_size, light_y - light_size,
        light_x + light_size, light_y + light_size
    ], fill=white)
    
    # رسم خطوط الذكاء الاصطناعي (دوائر متحدة المركز)
    for i in range(3):
        ai_radius = eye_radius + (i + 1) * 20
        if ai_radius < size // 2 - 12:
            draw.ellipse([
                center - ai_radius, center - ai_radius,
                center + ai_radius, center + ai_radius
            ], outline=accent_color, width=2)
    
    # إضافة رمز الحماية (درع صغير) في الزاوية
    shield_size = size // 6
    shield_x = size - shield_size - 16
    shield_y = 16
    
    # رسم الدرع
    shield_points = [
        (shield_x + shield_size // 2, shield_y),
        (shield_x + shield_size, shield_y + shield_size // 3),
        (shield_x + shield_size, shield_y + shield_size * 2 // 3),
        (shield_x + shield_size // 2, shield_y + shield_size),
        (shield_x, shield_y + shield_size * 2 // 3),
        (shield_x, shield_y + shield_size // 3)
    ]
    draw.polygon(shield_points, fill=accent_color, outline=white, width=2)
    
    # حفظ الأيقونة الرئيسية
    main_icon_path = os.path.join(icons_dir, "eyeguard_main_icon.png")
    image.save(main_icon_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_main_icon.png")
    
    return image

def create_app_icon_ico():
    """
    إنشاء ملف ICO للتطبيق
    """
    icons_dir = "assets/icons"
    
    # أحجام مختلفة للأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    print("🔧 إنشاء أيقونات بأحجام مختلفة...")
    
    for size in sizes:
        # إنشاء أيقونة بحجم معين
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # ألوان
        primary_color = (33, 150, 243)
        white = (255, 255, 255)
        dark_blue = (13, 71, 161)
        
        center = size // 2
        
        # خلفية دائرية
        bg_radius = size // 2 - 2
        draw.ellipse([2, 2, size-2, size-2], fill=primary_color)
        
        # العين
        eye_radius = size // 3
        if size < 32:
            eye_radius = size // 4
        
        eye_bbox = [
            center - eye_radius, center - eye_radius,
            center + eye_radius, center + eye_radius
        ]
        draw.ellipse(eye_bbox, fill=white, outline=dark_blue, width=max(1, size // 32))
        
        # البؤبؤ
        pupil_radius = size // 6
        if size < 32:
            pupil_radius = size // 8
            
        pupil_bbox = [
            center - pupil_radius, center - pupil_radius,
            center + pupil_radius, center + pupil_radius
        ]
        draw.ellipse(pupil_bbox, fill=dark_blue)
        
        # نقطة ضوء (للأحجام الكبيرة فقط)
        if size >= 32:
            light_size = max(1, size // 32)
            light_x = center - pupil_radius // 3
            light_y = center - pupil_radius // 3
            draw.ellipse([
                light_x - light_size, light_y - light_size,
                light_x + light_size, light_y + light_size
            ], fill=white)
        
        images.append(image)
        
        # حفظ كل حجم منفرد
        size_path = os.path.join(icons_dir, f"eyeguard_icon_{size}x{size}.png")
        image.save(size_path, "PNG")
        print(f"✅ تم إنشاء: eyeguard_icon_{size}x{size}.png")
    
    # إنشاء ملف ICO
    try:
        ico_path = os.path.join(icons_dir, "eyeguard_app.ico")
        images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
        print(f"✅ تم إنشاء: eyeguard_app.ico")
    except Exception as e:
        print(f"⚠️ تعذر إنشاء ملف ICO: {e}")

def create_logo_with_text():
    """
    إنشاء شعار مع النص
    """
    icons_dir = "assets/icons"
    
    print("📝 إنشاء شعار مع النص...")
    
    # حجم الشعار
    width, height = 500, 150
    image = Image.new('RGBA', (width, height), (255, 255, 255, 255))  # خلفية بيضاء
    draw = ImageDraw.Draw(image)
    
    # إنشاء أيقونة صغيرة للشعار
    icon_size = 100
    icon = Image.new('RGBA', (icon_size, icon_size), (0, 0, 0, 0))
    icon_draw = ImageDraw.Draw(icon)
    
    # ألوان
    primary_color = (33, 150, 243)
    secondary_color = (76, 175, 80)
    white = (255, 255, 255)
    dark_blue = (13, 71, 161)
    
    center = icon_size // 2
    
    # رسم الأيقونة
    bg_radius = icon_size // 2 - 4
    icon_draw.ellipse([4, 4, icon_size-4, icon_size-4], fill=primary_color)
    
    eye_radius = icon_size // 3
    eye_bbox = [center - eye_radius, center - eye_radius, center + eye_radius, center + eye_radius]
    icon_draw.ellipse(eye_bbox, fill=white, outline=dark_blue, width=2)
    
    iris_radius = icon_size // 4
    iris_bbox = [center - iris_radius, center - iris_radius, center + iris_radius, center + iris_radius]
    icon_draw.ellipse(iris_bbox, fill=secondary_color)
    
    pupil_radius = icon_size // 6
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    icon_draw.ellipse(pupil_bbox, fill=dark_blue)
    
    # لصق الأيقونة
    image.paste(icon, (25, 25), icon)
    
    # إضافة النص
    try:
        font_large = ImageFont.truetype("arial.ttf", 36)
        font_medium = ImageFont.truetype("arial.ttf", 20)
        font_small = ImageFont.truetype("arial.ttf", 14)
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # النص الرئيسي
    text_x = icon_size + 50
    draw.text((text_x, 30), "EyeGuard AI", fill=dark_blue, font=font_large)
    draw.text((text_x, 75), "حماية عيونك بالذكاء الاصطناعي", fill=primary_color, font=font_medium)
    draw.text((text_x, 105), "Eye Protection with Artificial Intelligence", fill=(100, 100, 100), font=font_small)
    
    # حفظ الشعار
    logo_path = os.path.join(icons_dir, "eyeguard_logo_with_text.png")
    image.save(logo_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_logo_with_text.png")

def create_simple_variants():
    """
    إنشاء متغيرات بسيطة
    """
    icons_dir = "assets/icons"
    
    print("🎨 إنشاء متغيرات بسيطة...")
    
    # نسخة للخلفية المظلمة
    size = 128
    dark_image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    dark_draw = ImageDraw.Draw(dark_image)
    
    # ألوان فاتحة للخلفية المظلمة
    light_blue = (100, 181, 246)
    light_green = (129, 199, 132)
    yellow = (255, 213, 79)
    white = (255, 255, 255)
    
    center = size // 2
    
    # خلفية
    bg_radius = size // 2 - 4
    dark_draw.ellipse([4, 4, size-4, size-4], fill=light_blue)
    
    # العين
    eye_radius = size // 3
    eye_bbox = [center - eye_radius, center - eye_radius, center + eye_radius, center + eye_radius]
    dark_draw.ellipse(eye_bbox, fill=white, outline=yellow, width=2)
    
    # القزحية
    iris_radius = size // 4
    iris_bbox = [center - iris_radius, center - iris_radius, center + iris_radius, center + iris_radius]
    dark_draw.ellipse(iris_bbox, fill=light_green)
    
    # البؤبؤ
    pupil_radius = size // 6
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    dark_draw.ellipse(pupil_bbox, fill=(30, 30, 30))
    
    # حفظ النسخة المظلمة
    dark_path = os.path.join(icons_dir, "eyeguard_icon_dark.png")
    dark_image.save(dark_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_icon_dark.png")
    
    # نسخة مبسطة جداً
    simple_image = Image.new('RGBA', (64, 64), (0, 0, 0, 0))
    simple_draw = ImageDraw.Draw(simple_image)
    
    # دائرة خارجية
    simple_draw.ellipse([2, 2, 62, 62], outline=(33, 150, 243), width=3)
    
    # العين المبسطة
    simple_draw.ellipse([16, 24, 48, 40], fill=(33, 150, 243))
    simple_draw.ellipse([28, 28, 36, 36], fill=(255, 255, 255))
    
    # حفظ النسخة المبسطة
    simple_path = os.path.join(icons_dir, "eyeguard_icon_simple.png")
    simple_image.save(simple_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_icon_simple.png")

def main():
    """
    الدالة الرئيسية
    """
    print("🎨 بدء إنشاء أيقونات EyeGuard AI...")
    print("=" * 50)
    
    # إنشاء الأيقونة الرئيسية
    create_main_icon()
    
    # إنشاء أيقونات بأحجام مختلفة وملف ICO
    create_app_icon_ico()
    
    # إنشاء شعار مع النص
    create_logo_with_text()
    
    # إنشاء متغيرات بسيطة
    create_simple_variants()
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء جميع الأيقونات بنجاح!")
    print("\nالملفات المُنشأة في assets/icons/:")
    print("  ├── eyeguard_main_icon.png (256x256)")
    print("  ├── eyeguard_app.ico (ملف ICO للويندوز)")
    print("  ├── eyeguard_logo_with_text.png (شعار مع النص)")
    print("  ├── eyeguard_icon_dark.png (للخلفية المظلمة)")
    print("  ├── eyeguard_icon_simple.png (نسخة مبسطة)")
    print("  └── أحجام مختلفة: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256")

if __name__ == "__main__":
    main()

# 🎨 أيقونات EyeGuard AI

هذا المجلد يحتوي على جميع أيقونات ورموز تطبيق EyeGuard AI.

## 📁 الملفات المتوفرة

### 🖼️ الأيقونات الرئيسية

| الملف | الحجم | الوصف | الاستخدام |
|-------|-------|--------|-----------|
| `eyeguard_main_icon.png` | 256×256 | الأيقونة الرئيسية عالية الجودة | العرض والتوثيق |
| `eyeguard_app.ico` | متعدد الأحجام | ملف ICO للويندوز | أيقونة التطبيق |
| `eyeguard_logo_with_text.png` | 500×150 | شعار مع النص | المواقع والعروض |

### 📐 أحجام مختلفة

| الملف | الحجم | الاستخدام |
|-------|-------|-----------|
| `eyeguard_icon_16x16.png` | 16×16 | أيقونة صغيرة جداً |
| `eyeguard_icon_32x32.png` | 32×32 | أيقونة صغيرة |
| `eyeguard_icon_48x48.png` | 48×48 | أيقونة متوسطة |
| `eyeguard_icon_64x64.png` | 64×64 | أيقونة كبيرة |
| `eyeguard_icon_128x128.png` | 128×128 | أيقونة كبيرة جداً |
| `eyeguard_icon_256x256.png` | 256×256 | أيقونة عالية الجودة |
| `eyeguard_icon_512x512.png` | 512×512 | أيقونة فائقة الجودة |

### 🎨 متغيرات التصميم

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `eyeguard_icon_dark.png` | نسخة للخلفية المظلمة | الثيمات المظلمة |
| `eyeguard_icon_simple.png` | نسخة مبسطة | الاستخدامات البسيطة |

## 🎯 تصميم الأيقونة

### 🎨 العناصر الأساسية
- **👁️ العين**: الرمز الرئيسي للتطبيق
- **🔵 الدائرة الزرقاء**: تمثل التقنية والذكاء الاصطناعي
- **🟢 القزحية الخضراء**: تمثل الصحة والسلامة
- **🛡️ رمز الحماية**: يدل على الحماية والأمان
- **✨ الدوائر المتحدة المركز**: تمثل تقنية الذكاء الاصطناعي

### 🎨 الألوان المستخدمة

| اللون | الكود | الاستخدام |
|-------|-------|-----------|
| أزرق EyeGuard | `#2196F3` | اللون الأساسي |
| أخضر الصحة | `#4CAF50` | القزحية والصحة |
| ذهبي الذكاء | `#FFC107` | رمز الحماية والذكاء |
| أزرق داكن | `#0D47A1` | البؤبؤ والحدود |
| أبيض | `#FFFFFF` | الصلبة ونقطة الضوء |

## 🔧 كيفية الاستخدام

### في التطبيق
```python
# تعيين أيقونة التطبيق
self.root.iconbitmap("assets/icons/eyeguard_app.ico")

# أو استخدام PNG
icon = tk.PhotoImage(file="assets/icons/eyeguard_icon_32x32.png")
self.root.iconphoto(True, icon)
```

### في المواقع
```html
<!-- Favicon -->
<link rel="icon" type="image/png" href="assets/icons/eyeguard_icon_32x32.png">

<!-- شعار الموقع -->
<img src="assets/icons/eyeguard_logo_with_text.png" alt="EyeGuard AI">
```

### في التوثيق
```markdown
![EyeGuard AI](assets/icons/eyeguard_main_icon.png)
```

## 📏 إرشادات الاستخدام

### ✅ الاستخدامات المناسبة
- أيقونة التطبيق الرسمية
- شعار في المواد التسويقية
- رمز في واجهات المستخدم
- أيقونة في متاجر التطبيقات

### ❌ الاستخدامات غير المناسبة
- تعديل الألوان الأساسية
- إضافة نصوص على الأيقونة
- استخدام أجزاء منفصلة من التصميم
- تشويه نسب الأيقونة

## 🔄 إعادة الإنشاء

لإعادة إنشاء الأيقونات:

```bash
# إنشاء جميع الأيقونات
python create_simple_icon.py

# أو الإصدار المتقدم
python create_icon.py
```

## 📋 متطلبات الإنشاء

- Python 3.8+
- Pillow (PIL)
- مساحة كافية في `assets/icons/`

## 🎨 تخصيص الأيقونة

يمكن تخصيص الأيقونة عبر تعديل الألوان في ملف `create_simple_icon.py`:

```python
# الألوان الأساسية
primary_color = (33, 150, 243)      # أزرق EyeGuard
secondary_color = (76, 175, 80)     # أخضر للصحة
accent_color = (255, 193, 7)        # ذهبي للذكاء الاصطناعي
```

## 📄 الترخيص

هذه الأيقونات جزء من مشروع EyeGuard AI مفتوح المصدر تحت رخصة MIT.

---

**تم إنشاؤها بـ ❤️ لمشروع EyeGuard AI**

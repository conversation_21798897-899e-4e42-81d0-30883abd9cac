# 👁️ EyeGuard AI
## حماية عيونك بالذكاء الاصطناعي

**EyeGuard AI** هو تطبيق ذكي مجاني مفتوح المصدر، مبني باستخدام Python، يساعد المستخدم على اكتشاف المشاكل الشائعة في العين من خلال تحليل صورة للعين، ويقترح بعدها علاج مبدئي بناءً على النتائج.

## ✨ الميزات الرئيسية

- 🔍 **تحليل ذكي للصور**: اكتشاف المشاكل الشائعة في العين باستخدام الذكاء الاصطناعي
- 💊 **اقتراحات علاجية**: توصيات مبدئية بناءً على التشخيص
- 🎨 **واجهة عصرية**: تصميم جذاب مع دعم الوضع المظلم
- 🔊 **تحويل نص لصوت**: إمكانية سماع النتائج صوتياً
- 💾 **حفظ النتائج**: إمكانية حفظ التشخيص كصورة
- 🌐 **يعمل بدون إنترنت**: لا يحتاج اتصال بالإنترنت

## 🎯 الحالات المدعومة

- ✅ العين الطبيعية (Normal)
- 🔴 احمرار والتهاب العين
- ⚪ الاشتباه في المياه البيضاء
- 🩸 مشاكل الشبكية
- 💧 جفاف العين

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**
- **PyTorch** - للذكاء الاصطناعي
- **Tkinter** - واجهة المستخدم
- **Pillow (PIL)** - معالجة الصور
- **pyttsx3** - تحويل النص لصوت

## 📦 التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python main.py
```

## 📁 هيكل المشروع

```
EyeGuard-AI/
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── requirements.txt        # متطلبات المشروع
├── README.md              # ملف التوثيق
├── models/                # نماذج الذكاء الاصطناعي
│   ├── eye_classifier.py  # نموذج التصنيف
│   └── trained_model.pth  # النموذج المدرب
├── gui/                   # واجهة المستخدم
│   ├── main_window.py     # النافذة الرئيسية
│   ├── components/        # مكونات الواجهة
│   └── themes/           # ملفات الثيمات
├── utils/                 # أدوات مساعدة
│   ├── image_processor.py # معالجة الصور
│   ├── audio_handler.py   # التعامل مع الصوت
│   └── treatment_guide.py # دليل العلاج
├── assets/               # الملفات المساعدة
│   ├── icons/           # الأيقونات
│   ├── sounds/          # الأصوات
│   └── sample_images/   # صور للاختبار
└── tests/               # اختبارات المشروع
    └── test_classifier.py
```

## ⚠️ تنبيه طبي مهم

هذا التطبيق مخصص للاستخدام التعليمي والتوعوي فقط. النتائج المعروضة لا تغني عن استشارة طبيب مختص. في حالة وجود أي مشاكل في العين، يُنصح بزيارة طبيب العيون فوراً.

## 📄 الترخيص

هذا المشروع مفتوح المصدر تحت رخصة MIT.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال أي تحديثات.

---
**تم تطويره بـ ❤️ لخدمة المجتمع**

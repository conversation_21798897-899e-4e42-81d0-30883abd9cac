#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لمكونات EyeGuard AI
Quick test for EyeGuard AI components
"""

import sys
import os

def test_basic_imports():
    """
    اختبار الاستيرادات الأساسية
    """
    print("🔍 اختبار الاستيرادات الأساسية...")
    
    try:
        import tkinter as tk
        print("✅ tkinter متاح")
    except ImportError:
        print("❌ tkinter غير متاح")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow متاح")
    except ImportError:
        print("❌ Pillow غير متاح")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy متاح")
    except ImportError:
        print("❌ NumPy غير متاح")
        return False
    
    return True

def test_project_structure():
    """
    اختبار هيكل المشروع
    """
    print("\n📁 اختبار هيكل المشروع...")
    
    required_files = [
        "main.py",
        "config.py",
        "requirements.txt",
        "README.md",
        "models/__init__.py",
        "models/eye_classifier.py",
        "utils/__init__.py",
        "utils/image_processor.py",
        "utils/audio_handler.py",
        "utils/treatment_guide.py",
        "gui/__init__.py",
        "gui/main_window.py",
        "tests/__init__.py",
        "tests/test_classifier.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_sample_images():
    """
    اختبار صور العينات
    """
    print("\n🖼️ اختبار صور العينات...")
    
    sample_dir = "assets/sample_images"
    if not os.path.exists(sample_dir):
        print(f"❌ مجلد الصور غير موجود: {sample_dir}")
        return False
    
    expected_images = [
        "normal_eye_sample.jpg",
        "red_eye_sample.jpg", 
        "cataract_eye_sample.jpg",
        "retinal_eye_sample.jpg",
        "dry_eye_sample.jpg"
    ]
    
    missing_images = []
    for image_name in expected_images:
        image_path = os.path.join(sample_dir, image_name)
        if os.path.exists(image_path):
            print(f"✅ {image_name}")
        else:
            print(f"❌ {image_name}")
            missing_images.append(image_name)
    
    return len(missing_images) == 0

def test_simple_classifier():
    """
    اختبار المصنف البسيط
    """
    print("\n🤖 اختبار المصنف البسيط...")
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from models.eye_classifier import SimpleEyeClassifier
        
        classifier = SimpleEyeClassifier()
        print("✅ تم إنشاء المصنف")
        
        # اختبار مع صورة عينة
        sample_image = "assets/sample_images/normal_eye_sample.jpg"
        if os.path.exists(sample_image):
            prediction = classifier.predict(sample_image)
            print(f"✅ تم التنبؤ: {prediction['class_name']} ({prediction['confidence']:.1%})")
            return True
        else:
            print("⚠️ لا توجد صورة عينة للاختبار")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في المصنف: {e}")
        return False

def test_image_processor():
    """
    اختبار معالج الصور
    """
    print("\n🖼️ اختبار معالج الصور...")
    
    try:
        from utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        print("✅ تم إنشاء معالج الصور")
        
        # اختبار مع صورة عينة
        sample_image = "assets/sample_images/normal_eye_sample.jpg"
        if os.path.exists(sample_image):
            is_valid = processor.is_valid_image(sample_image)
            print(f"✅ صحة الصورة: {is_valid}")
            
            info = processor.get_image_info(sample_image)
            if info:
                print(f"✅ معلومات الصورة: {info['width']}x{info['height']}")
            
            return True
        else:
            print("⚠️ لا توجد صورة عينة للاختبار")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في معالج الصور: {e}")
        return False

def test_treatment_guide():
    """
    اختبار دليل العلاج
    """
    print("\n💊 اختبار دليل العلاج...")
    
    try:
        from utils.treatment_guide import TreatmentGuide
        
        guide = TreatmentGuide()
        print("✅ تم إنشاء دليل العلاج")
        
        # اختبار الحصول على معلومات العلاج
        info = guide.get_treatment_info('طبيعي')
        print(f"✅ معلومات العلاج: {info['title']}")
        
        urgency = guide.get_urgency_level('مياه بيضاء')
        print(f"✅ مستوى الإلحاح: {urgency}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في دليل العلاج: {e}")
        return False

def test_gui_components():
    """
    اختبار مكونات الواجهة (بدون تشغيل)
    """
    print("\n🖥️ اختبار مكونات الواجهة...")
    
    try:
        # اختبار استيراد الواجهة الرئيسية
        from gui.main_window import EyeGuardMainWindow
        print("✅ تم استيراد الواجهة الرئيسية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مكونات الواجهة: {e}")
        return False

def main():
    """
    تشغيل جميع الاختبارات
    """
    print("🚀 بدء الاختبار السريع لـ EyeGuard AI")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("هيكل المشروع", test_project_structure),
        ("صور العينات", test_sample_images),
        ("المصنف البسيط", test_simple_classifier),
        ("معالج الصور", test_image_processor),
        ("دليل العلاج", test_treatment_guide),
        ("مكونات الواجهة", test_gui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل")
        print("\nلتشغيل التطبيق:")
        print("python main.py")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
معالج الصور لتطبيق EyeGuard AI
Image Processing utilities for EyeGuard AI
"""

from PIL import Image, ImageEnhance, ImageFilter, ImageDraw, ImageFont
import numpy as np
import cv2
import os

class ImageProcessor:
    """
    فئة معالجة الصور
    """
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    def is_valid_image(self, file_path):
        """
        التحقق من صحة ملف الصورة
        """
        if not os.path.exists(file_path):
            return False
        
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.supported_formats
    
    def enhance_image(self, image_path, output_path=None):
        """
        تحسين جودة الصورة للتحليل
        """
        try:
            image = Image.open(image_path)
            
            # تحسين السطوع والتباين
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.1)
            
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            # تحسين الحدة
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # تطبيق فلتر لتقليل الضوضاء
            image = image.filter(ImageFilter.SMOOTH_MORE)
            
            if output_path:
                image.save(output_path)
            
            return image
            
        except Exception as e:
            print(f"خطأ في تحسين الصورة: {e}")
            return None
    
    def resize_image(self, image_path, size=(224, 224)):
        """
        تغيير حجم الصورة
        """
        try:
            image = Image.open(image_path)
            resized_image = image.resize(size, Image.Resampling.LANCZOS)
            return resized_image
        except Exception as e:
            print(f"خطأ في تغيير حجم الصورة: {e}")
            return None
    
    def detect_eye_region(self, image_path):
        """
        اكتشاف منطقة العين في الصورة (مبسط)
        """
        try:
            # قراءة الصورة باستخدام OpenCV
            image = cv2.imread(image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # تحميل مصنف العين من OpenCV
            eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
            
            # اكتشاف العيون
            eyes = eye_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(eyes) > 0:
                # أخذ أول عين مكتشفة
                (x, y, w, h) = eyes[0]
                
                # قص منطقة العين
                eye_region = image[y:y+h, x:x+w]
                
                # تحويل إلى PIL Image
                eye_region_rgb = cv2.cvtColor(eye_region, cv2.COLOR_BGR2RGB)
                return Image.fromarray(eye_region_rgb)
            else:
                # إذا لم يتم اكتشاف عين، إرجاع الصورة الأصلية
                return Image.open(image_path)
                
        except Exception as e:
            print(f"خطأ في اكتشاف منطقة العين: {e}")
            return Image.open(image_path)
    
    def create_result_image(self, original_image_path, prediction_result, output_path):
        """
        إنشاء صورة النتيجة مع التشخيص
        """
        try:
            # فتح الصورة الأصلية
            image = Image.open(original_image_path)
            
            # تغيير حجم الصورة إذا كانت كبيرة جداً
            if image.width > 800 or image.height > 600:
                image.thumbnail((800, 600), Image.Resampling.LANCZOS)
            
            # إنشاء صورة جديدة مع مساحة للنص
            result_width = max(image.width, 400)
            result_height = image.height + 200
            
            result_image = Image.new('RGB', (result_width, result_height), 'white')
            
            # لصق الصورة الأصلية
            x_offset = (result_width - image.width) // 2
            result_image.paste(image, (x_offset, 20))
            
            # إضافة النص
            draw = ImageDraw.Draw(result_image)
            
            try:
                # محاولة استخدام خط عربي
                font_large = ImageFont.truetype("arial.ttf", 24)
                font_medium = ImageFont.truetype("arial.ttf", 18)
                font_small = ImageFont.truetype("arial.ttf", 14)
            except:
                # استخدام الخط الافتراضي
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # عنوان النتيجة
            title = "EyeGuard AI - نتيجة التحليل"
            title_bbox = draw.textbbox((0, 0), title, font=font_large)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (result_width - title_width) // 2
            draw.text((title_x, image.height + 30), title, fill='black', font=font_large)
            
            # التشخيص
            diagnosis = f"التشخيص: {prediction_result['class_name']}"
            draw.text((20, image.height + 70), diagnosis, fill='blue', font=font_medium)
            
            # مستوى الثقة
            confidence = f"مستوى الثقة: {prediction_result['confidence']:.1%}"
            draw.text((20, image.height + 100), confidence, fill='green', font=font_medium)
            
            # تاريخ التحليل
            from datetime import datetime
            date_str = datetime.now().strftime("%Y-%m-%d %H:%M")
            date_text = f"تاريخ التحليل: {date_str}"
            draw.text((20, image.height + 130), date_text, fill='gray', font=font_small)
            
            # تحذير طبي
            warning = "تنبيه: هذا التشخيص للاستخدام التعليمي فقط - استشر طبيب مختص"
            warning_bbox = draw.textbbox((0, 0), warning, font=font_small)
            warning_width = warning_bbox[2] - warning_bbox[0]
            warning_x = (result_width - warning_width) // 2
            draw.text((warning_x, image.height + 160), warning, fill='red', font=font_small)
            
            # حفظ الصورة
            result_image.save(output_path)
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء صورة النتيجة: {e}")
            return False
    
    def get_image_info(self, image_path):
        """
        الحصول على معلومات الصورة
        """
        try:
            image = Image.open(image_path)
            return {
                'width': image.width,
                'height': image.height,
                'format': image.format,
                'mode': image.mode,
                'size_mb': os.path.getsize(image_path) / (1024 * 1024)
            }
        except Exception as e:
            print(f"خطأ في قراءة معلومات الصورة: {e}")
            return None

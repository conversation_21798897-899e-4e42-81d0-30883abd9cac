#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض أيقونات EyeGuard AI
Display EyeGuard AI icons
"""

import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import os
import glob

class IconViewer:
    """
    عارض الأيقونات
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_icons()
    
    def setup_window(self):
        """
        إعداد النافذة
        """
        self.root.title("🎨 أيقونات EyeGuard AI")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تعيين أيقونة النافذة
        try:
            icon_path = "assets/icons/eyeguard_app.ico"
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        """
        # العنوان
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(fill='x', pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="🎨 أيقونات EyeGuard AI",
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2196F3'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="مجموعة كاملة من الأيقونات والرموز",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack()
        
        # إطار التمرير
        canvas_frame = tk.Frame(self.root)
        canvas_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Canvas مع شريط التمرير
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        scrollbar = ttk.Scrollbar(canvas_frame, orient='vertical', command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='white')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """
        التعامل مع عجلة الماوس
        """
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def load_icons(self):
        """
        تحميل وعرض الأيقونات
        """
        icons_dir = "assets/icons"
        
        if not os.path.exists(icons_dir):
            error_label = tk.Label(
                self.scrollable_frame,
                text="❌ مجلد الأيقونات غير موجود\nقم بتشغيل create_simple_icon.py أولاً",
                font=("Arial", 14),
                bg='white',
                fg='red'
            )
            error_label.pack(pady=50)
            return
        
        # البحث عن ملفات الأيقونات
        icon_files = []
        
        # أنواع الملفات المدعومة
        extensions = ['*.png', '*.ico']
        
        for ext in extensions:
            icon_files.extend(glob.glob(os.path.join(icons_dir, ext)))
        
        if not icon_files:
            no_icons_label = tk.Label(
                self.scrollable_frame,
                text="❌ لا توجد أيقونات\nقم بتشغيل create_simple_icon.py لإنشاء الأيقونات",
                font=("Arial", 14),
                bg='white',
                fg='orange'
            )
            no_icons_label.pack(pady=50)
            return
        
        # ترتيب الملفات
        icon_files.sort()
        
        # عرض الأيقونات
        row = 0
        col = 0
        max_cols = 3
        
        for icon_file in icon_files:
            self.display_icon(icon_file, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
    
    def display_icon(self, icon_path, row, col):
        """
        عرض أيقونة واحدة
        """
        try:
            # إطار للأيقونة
            icon_frame = tk.Frame(
                self.scrollable_frame,
                bg='white',
                relief='solid',
                bd=1,
                padx=10,
                pady=10
            )
            icon_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
            
            # اسم الملف
            filename = os.path.basename(icon_path)
            name_label = tk.Label(
                icon_frame,
                text=filename,
                font=("Arial", 10, "bold"),
                bg='white',
                fg='#333333'
            )
            name_label.pack(pady=(0, 5))
            
            # تحميل وعرض الأيقونة
            if icon_path.endswith('.ico'):
                # للملفات ICO
                try:
                    # محاولة تحميل ICO كـ PNG
                    img = Image.open(icon_path)
                    img = img.resize((64, 64), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(img)
                except:
                    # إذا فشل، عرض رمز بديل
                    photo = None
            else:
                # للملفات PNG
                img = Image.open(icon_path)
                
                # تحديد حجم العرض
                display_size = 64
                if img.width > display_size or img.height > display_size:
                    img.thumbnail((display_size, display_size), Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(img)
            
            if photo:
                icon_label = tk.Label(
                    icon_frame,
                    image=photo,
                    bg='white'
                )
                icon_label.image = photo  # الاحتفاظ بمرجع
                icon_label.pack()
            else:
                # رمز بديل للملفات التي لا يمكن عرضها
                placeholder_label = tk.Label(
                    icon_frame,
                    text="🖼️\nICO",
                    font=("Arial", 12),
                    bg='white',
                    fg='gray',
                    width=8,
                    height=4
                )
                placeholder_label.pack()
            
            # معلومات الملف
            try:
                file_size = os.path.getsize(icon_path)
                if file_size < 1024:
                    size_text = f"{file_size} B"
                elif file_size < 1024 * 1024:
                    size_text = f"{file_size // 1024} KB"
                else:
                    size_text = f"{file_size // (1024 * 1024)} MB"
                
                if not icon_path.endswith('.ico'):
                    img_info = Image.open(icon_path)
                    info_text = f"{img_info.width}×{img_info.height}\n{size_text}"
                else:
                    info_text = f"ICO\n{size_text}"
                
                info_label = tk.Label(
                    icon_frame,
                    text=info_text,
                    font=("Arial", 8),
                    bg='white',
                    fg='gray'
                )
                info_label.pack(pady=(5, 0))
                
            except Exception as e:
                print(f"خطأ في قراءة معلومات {filename}: {e}")
        
        except Exception as e:
            print(f"خطأ في عرض {icon_path}: {e}")
            
            # عرض رسالة خطأ
            error_frame = tk.Frame(
                self.scrollable_frame,
                bg='#ffebee',
                relief='solid',
                bd=1,
                padx=10,
                pady=10
            )
            error_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
            
            error_label = tk.Label(
                error_frame,
                text=f"❌ خطأ في تحميل\n{os.path.basename(icon_path)}",
                font=("Arial", 10),
                bg='#ffebee',
                fg='red'
            )
            error_label.pack()
    
    def run(self):
        """
        تشغيل العارض
        """
        self.root.mainloop()

def main():
    """
    الدالة الرئيسية
    """
    print("🎨 تشغيل عارض أيقونات EyeGuard AI...")
    
    # التحقق من وجود مجلد الأيقونات
    if not os.path.exists("assets/icons"):
        print("❌ مجلد الأيقونات غير موجود")
        print("قم بتشغيل الأمر التالي لإنشاء الأيقونات:")
        print("python create_simple_icon.py")
        return
    
    # تشغيل العارض
    viewer = IconViewer()
    viewer.run()

if __name__ == "__main__":
    main()

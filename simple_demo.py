#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي مبسط لـ EyeGuard AI
Simple demo for EyeGuard AI (without heavy dependencies)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import os
import random

class SimpleEyeGuardDemo:
    """
    عرض توضيحي مبسط لتطبيق EyeGuard AI
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.current_image_path = None
        self.create_widgets()
        
        # بيانات التصنيف المبسطة
        self.class_names = [
            'طبيعي',           # Normal
            'احمرار والتهاب',   # Red/Inflammation  
            'مياه بيضاء',       # Cataract
            'مشاكل الشبكية',    # Retinal Issues
            'جفاف العين'        # Dry Eye
        ]
        
        self.treatments = {
            'طبيعي': {
                'description': 'العين تبدو في حالة طبيعية وصحية',
                'recommendations': [
                    '✅ حافظ على نظافة العين',
                    '✅ استخدم نظارات شمسية عند التعرض للشمس',
                    '✅ تناول الأطعمة الغنية بفيتامين A'
                ],
                'urgency': 'منخفض',
                'color': '#4CAF50'
            },
            'احمرار والتهاب': {
                'description': 'يوجد احمرار أو التهاب قد يكون بسبب عدوى أو تهيج',
                'recommendations': [
                    '🧼 اغسل يديك قبل لمس العين',
                    '💧 استخدم كمادات باردة لمدة 10-15 دقيقة',
                    '💊 تجنب فرك العين'
                ],
                'urgency': 'متوسط',
                'color': '#FF9800'
            },
            'مياه بيضاء': {
                'description': 'قد يكون هناك عتامة في عدسة العين (المياه البيضاء)',
                'recommendations': [
                    '👨‍⚕️ راجع طبيب العيون فوراً للتشخيص الدقيق',
                    '🕶️ استخدم نظارات شمسية لحماية العين',
                    '🚭 تجنب التدخين'
                ],
                'urgency': 'عالي',
                'color': '#F44336'
            },
            'مشاكل الشبكية': {
                'description': 'قد يكون هناك مشاكل في شبكية العين تحتاج فحص طبي',
                'recommendations': [
                    '🚨 راجع طبيب العيون المختص فوراً',
                    '📱 تجنب الاستخدام المفرط للشاشات',
                    '🩺 افحص ضغط الدم بانتظام'
                ],
                'urgency': 'عالي جداً',
                'color': '#D32F2F'
            },
            'جفاف العين': {
                'description': 'العين تعاني من نقص في الترطيب الطبيعي',
                'recommendations': [
                    '💧 استخدم قطرات العين المرطبة بانتظام',
                    '💻 اتبع قاعدة 20-20-20',
                    '💧 اشرب كمية كافية من الماء'
                ],
                'urgency': 'منخفض إلى متوسط',
                'color': '#2196F3'
            }
        }
    
    def setup_window(self):
        """
        إعداد النافذة الرئيسية
        """
        self.root.title("👁️ EyeGuard AI - عرض توضيحي")
        self.root.geometry("900x600")
        self.root.configure(bg='#f0f0f0')

        # تعيين أيقونة التطبيق
        try:
            icon_path = "assets/icons/eyeguard_app.ico"
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
            else:
                # محاولة استخدام أيقونة PNG
                icon_png_path = "assets/icons/eyeguard_icon_32x32.png"
                if os.path.exists(icon_png_path):
                    icon_image = tk.PhotoImage(file=icon_png_path)
                    self.root.iconphoto(True, icon_image)
        except Exception as e:
            print(f"تعذر تحميل الأيقونة: {e}")
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        """
        # العنوان
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(fill='x', pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="👁️ EyeGuard AI - عرض توضيحي",
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2196F3'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="حماية عيونك بالذكاء الاصطناعي",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack()
        
        # إطار الصورة
        self.image_frame = tk.Frame(self.root, bg='white', relief='sunken', bd=2)
        self.image_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.image_display = tk.Label(
            self.image_frame,
            text="اختر صورة للعين لبدء التحليل\n\nالصيغ المدعومة: JPG, PNG, BMP",
            font=("Arial", 12),
            bg='white',
            fg='gray',
            width=50,
            height=15,
            relief='solid',
            bd=2
        )
        self.image_display.pack(pady=20)
        
        # الأزرار
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        self.select_btn = tk.Button(
            buttons_frame,
            text="📁 اختيار صورة",
            command=self.select_image,
            font=("Arial", 12, "bold"),
            bg='#4CAF50',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        self.select_btn.pack(side='left', padx=5)
        
        self.analyze_btn = tk.Button(
            buttons_frame,
            text="🔍 تحليل الصورة",
            command=self.analyze_image,
            font=("Arial", 12, "bold"),
            bg='#2196F3',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2,
            state='disabled'
        )
        self.analyze_btn.pack(side='left', padx=5)
        
        info_btn = tk.Button(
            buttons_frame,
            text="ℹ️ معلومات",
            command=self.show_info,
            font=("Arial", 12, "bold"),
            bg='#607D8B',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        info_btn.pack(side='right', padx=5)
        
        # النتائج
        results_frame = tk.Frame(self.root, bg='white', relief='sunken', bd=2)
        results_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        results_label = tk.Label(
            results_frame,
            text="📊 نتائج التحليل والتوصيات",
            font=("Arial", 14, "bold"),
            bg='white',
            fg='#333333'
        )
        results_label.pack(pady=5)
        
        self.results_text = scrolledtext.ScrolledText(
            results_frame,
            font=("Arial", 11),
            bg='white',
            fg='#333333',
            height=8,
            wrap='word',
            state='disabled'
        )
        self.results_text.pack(fill='both', expand=True, padx=10, pady=5)
        
        # رسالة ترحيب
        welcome_text = """مرحباً بك في EyeGuard AI! 👋

هذا عرض توضيحي مبسط للتطبيق.

لبدء التحليل:
1. اضغط على 'اختيار صورة'
2. اختر صورة واضحة للعين
3. اضغط على 'تحليل الصورة'

⚠️ تنبيه: هذا عرض توضيحي فقط. النتائج عشوائية وليست تشخيص طبي حقيقي.
للحصول على تشخيص دقيق، استشر طبيب العيون."""
        
        self.update_results_text(welcome_text)
    
    def select_image(self):
        """
        اختيار صورة للتحليل
        """
        file_types = [
            ("صور", "*.jpg *.jpeg *.png *.bmp"),
            ("JPEG", "*.jpg *.jpeg"),
            ("PNG", "*.png"),
            ("BMP", "*.bmp"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر صورة العين",
            filetypes=file_types,
            initialdir="assets/sample_images" if os.path.exists("assets/sample_images") else os.path.expanduser("~")
        )
        
        if file_path:
            try:
                self.current_image_path = file_path
                self.display_image(file_path)
                self.analyze_btn.config(state='normal')
                
                info_text = f"تم اختيار الصورة:\n{os.path.basename(file_path)}\n\nاضغط على 'تحليل الصورة' للمتابعة"
                self.update_results_text(info_text)
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")
    
    def display_image(self, image_path):
        """
        عرض الصورة في الواجهة
        """
        try:
            image = Image.open(image_path)
            
            # تغيير حجم الصورة للعرض
            display_size = (300, 200)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            photo = ImageTk.PhotoImage(image)
            
            self.image_display.config(image=photo, text="")
            self.image_display.image = photo
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الصورة: {e}")
    
    def analyze_image(self):
        """
        تحليل الصورة (محاكاة)
        """
        if not self.current_image_path:
            messagebox.showwarning("تحذير", "يرجى اختيار صورة أولاً")
            return
        
        # محاكاة التحليل
        self.analyze_btn.config(state='disabled', text="🔄 جاري التحليل...")
        self.root.update()
        
        # تأخير للمحاكاة
        self.root.after(2000, self.complete_analysis)
    
    def complete_analysis(self):
        """
        إكمال التحليل وعرض النتائج
        """
        # اختيار نتيجة عشوائية للعرض التوضيحي
        filename = os.path.basename(self.current_image_path).lower()
        
        # محاولة التخمين من اسم الملف
        if 'normal' in filename or 'طبيعي' in filename:
            predicted_class = 'طبيعي'
            confidence = random.uniform(0.85, 0.95)
        elif 'red' in filename or 'احمرار' in filename:
            predicted_class = 'احمرار والتهاب'
            confidence = random.uniform(0.75, 0.90)
        elif 'cataract' in filename or 'مياه' in filename:
            predicted_class = 'مياه بيضاء'
            confidence = random.uniform(0.70, 0.85)
        elif 'retinal' in filename or 'شبكية' in filename:
            predicted_class = 'مشاكل الشبكية'
            confidence = random.uniform(0.65, 0.80)
        elif 'dry' in filename or 'جفاف' in filename:
            predicted_class = 'جفاف العين'
            confidence = random.uniform(0.70, 0.88)
        else:
            predicted_class = random.choice(self.class_names)
            confidence = random.uniform(0.60, 0.90)
        
        # عرض النتائج
        self.display_results(predicted_class, confidence)
        
        # إعادة تفعيل الزر
        self.analyze_btn.config(state='normal', text="🔍 تحليل الصورة")
    
    def display_results(self, class_name, confidence):
        """
        عرض نتائج التحليل
        """
        treatment_info = self.treatments.get(class_name, {})
        
        result_text = f"🎯 نتيجة التحليل:\n"
        result_text += f"التشخيص: {class_name}\n"
        result_text += f"مستوى الثقة: {confidence:.1%}\n\n"
        
        result_text += f"📝 الوصف:\n{treatment_info.get('description', 'غير متوفر')}\n\n"
        
        result_text += "💡 التوصيات:\n"
        for recommendation in treatment_info.get('recommendations', []):
            result_text += f"   {recommendation}\n"
        
        result_text += f"\n⚠️ مستوى الإلحاح: {treatment_info.get('urgency', 'غير محدد')}\n\n"
        
        result_text += "="*50 + "\n"
        result_text += "⚠️ تنبيه طبي مهم:\n"
        result_text += "هذا عرض توضيحي فقط والنتائج عشوائية.\n"
        result_text += "للحصول على تشخيص دقيق، استشر طبيب العيون المختص."
        
        self.update_results_text(result_text)
    
    def show_info(self):
        """
        عرض معلومات التطبيق
        """
        info_text = """👁️ EyeGuard AI - عرض توضيحي

🎯 الهدف:
تطبيق ذكي لاكتشاف المشاكل الشائعة في العين وتقديم نصائح علاجية.

✨ الميزات (في النسخة الكاملة):
• تحليل ذكي للصور باستخدام الذكاء الاصطناعي
• اكتشاف 5 حالات مختلفة للعين
• نصائح علاجية مفصلة
• تحويل النتائج إلى كلام
• حفظ النتائج كصورة

🔍 الحالات المدعومة:
• العين الطبيعية
• الاحمرار والالتهاب
• المياه البيضاء
• مشاكل الشبكية
• جفاف العين

⚠️ تنبيه مهم:
هذا عرض توضيحي فقط. النتائج عشوائية وليست تشخيص طبي حقيقي.

🛠️ التقنيات المستخدمة:
• Python & PyTorch للذكاء الاصطناعي
• Tkinter لواجهة المستخدم
• PIL لمعالجة الصور

© 2024 EyeGuard AI - مفتوح المصدر"""
        
        messagebox.showinfo("معلومات التطبيق", info_text)
    
    def update_results_text(self, text):
        """
        تحديث نص النتائج
        """
        self.results_text.config(state='normal')
        self.results_text.delete('1.0', tk.END)
        self.results_text.insert('1.0', text)
        self.results_text.config(state='disabled')
    
    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()

if __name__ == "__main__":
    print("🚀 تشغيل العرض التوضيحي لـ EyeGuard AI...")
    app = SimpleEyeGuardDemo()
    app.run()

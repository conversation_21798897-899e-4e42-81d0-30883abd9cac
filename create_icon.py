#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة تطبيق EyeGuard AI
Create EyeGuard AI application icon
"""

from PIL import Image, ImageDraw, ImageFont
import os
import math

def create_app_icon():
    """
    إنشاء أيقونة التطبيق الرئيسية
    """
    # إنشاء مجلد الأيقونات
    icons_dir = "assets/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    # أحجام الأيقونات المختلفة
    sizes = [16, 32, 48, 64, 128, 256, 512]
    
    print("🎨 إنشاء أيقونة EyeGuard AI...")
    
    for size in sizes:
        create_icon_size(size, icons_dir)
    
    # إنشاء ملف ICO للويندوز
    create_ico_file(icons_dir)
    
    print("✅ تم إنشاء جميع الأيقونات بنجاح!")

def create_icon_size(size, output_dir):
    """
    إنشاء أيقونة بحجم معين
    """
    # إنشاء صورة مربعة بخلفية شفافة
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # الألوان
    primary_color = (33, 150, 243)      # أزرق EyeGuard
    secondary_color = (76, 175, 80)     # أخضر للصحة
    accent_color = (255, 193, 7)        # ذهبي للذكاء الاصطناعي
    white = (255, 255, 255)
    dark_blue = (13, 71, 161)
    
    # حساب المقاييس بناءً على الحجم
    center = size // 2
    eye_radius = size // 3
    pupil_radius = size // 6
    iris_radius = size // 4
    
    # رسم الخلفية الدائرية
    bg_radius = size // 2 - 2
    draw.ellipse([2, 2, size-2, size-2], fill=primary_color)
    
    # رسم حلقة داخلية
    inner_ring = size // 2 - size // 8
    draw.ellipse([
        center - inner_ring, center - inner_ring,
        center + inner_ring, center + inner_ring
    ], outline=white, width=max(1, size // 32))
    
    # رسم العين الخارجية (الصلبة)
    eye_bbox = [
        center - eye_radius, center - eye_radius,
        center + eye_radius, center + eye_radius
    ]
    draw.ellipse(eye_bbox, fill=white, outline=dark_blue, width=max(1, size // 64))
    
    # رسم القزحية
    iris_bbox = [
        center - iris_radius, center - iris_radius,
        center + iris_radius, center + iris_radius
    ]
    draw.ellipse(iris_bbox, fill=secondary_color, outline=dark_blue, width=max(1, size // 128))
    
    # رسم البؤبؤ
    pupil_bbox = [
        center - pupil_radius, center - pupil_radius,
        center + pupil_radius, center + pupil_radius
    ]
    draw.ellipse(pupil_bbox, fill=dark_blue)
    
    # إضافة نقطة ضوء في البؤبؤ
    light_size = max(2, size // 32)
    light_x = center - pupil_radius // 3
    light_y = center - pupil_radius // 3
    draw.ellipse([
        light_x - light_size, light_y - light_size,
        light_x + light_size, light_y + light_size
    ], fill=white)
    
    # رسم خطوط الذكاء الاصطناعي (دوائر متحدة المركز)
    if size >= 64:
        for i in range(3):
            ai_radius = eye_radius + (i + 1) * (size // 16)
            if ai_radius < size // 2 - 4:
                draw.ellipse([
                    center - ai_radius, center - ai_radius,
                    center + ai_radius, center + ai_radius
                ], outline=accent_color, width=max(1, size // 128))
    
    # إضافة رمز الحماية (درع صغير) في الزاوية
    if size >= 48:
        shield_size = size // 8
        shield_x = size - shield_size - 4
        shield_y = 4
        
        # رسم الدرع
        shield_points = [
            (shield_x + shield_size // 2, shield_y),
            (shield_x + shield_size, shield_y + shield_size // 3),
            (shield_x + shield_size, shield_y + shield_size * 2 // 3),
            (shield_x + shield_size // 2, shield_y + shield_size),
            (shield_x, shield_y + shield_size * 2 // 3),
            (shield_x, shield_y + shield_size // 3)
        ]
        draw.polygon(shield_points, fill=accent_color, outline=white, width=1)
    
    # حفظ الأيقونة
    filename = f"eyeguard_icon_{size}x{size}.png"
    filepath = os.path.join(output_dir, filename)
    image.save(filepath, "PNG")
    print(f"✅ تم إنشاء: {filename}")
    
    return image

def create_ico_file(icons_dir):
    """
    إنشاء ملف ICO للويندوز
    """
    try:
        # تحميل الأيقونات بأحجام مختلفة
        icon_sizes = [16, 32, 48, 64, 128, 256]
        images = []
        
        for size in icon_sizes:
            icon_path = os.path.join(icons_dir, f"eyeguard_icon_{size}x{size}.png")
            if os.path.exists(icon_path):
                img = Image.open(icon_path)
                images.append(img)
        
        if images:
            # حفظ كملف ICO
            ico_path = os.path.join(icons_dir, "eyeguard_icon.ico")
            images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
            print(f"✅ تم إنشاء: eyeguard_icon.ico")
        
    except Exception as e:
        print(f"⚠️ تعذر إنشاء ملف ICO: {e}")

def create_logo_variants():
    """
    إنشاء متغيرات مختلفة من الشعار
    """
    icons_dir = "assets/icons"
    
    print("\n🎨 إنشاء متغيرات الشعار...")
    
    # شعار مع النص
    create_logo_with_text(icons_dir)
    
    # شعار للخلفية المظلمة
    create_dark_variant(icons_dir)
    
    # شعار مبسط
    create_simple_variant(icons_dir)

def create_logo_with_text(output_dir):
    """
    إنشاء شعار مع النص
    """
    # حجم الشعار
    width, height = 400, 120
    image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # رسم الأيقونة على اليسار
    icon_size = 80
    icon_image = create_icon_size(icon_size, "temp")
    
    # إنشاء أيقونة مؤقتة في الذاكرة
    icon = Image.new('RGBA', (icon_size, icon_size), (0, 0, 0, 0))
    icon_draw = ImageDraw.Draw(icon)
    
    # ألوان
    primary_color = (33, 150, 243)
    white = (255, 255, 255)
    dark_blue = (13, 71, 161)
    
    # رسم أيقونة مبسطة
    center = icon_size // 2
    eye_radius = icon_size // 3
    
    # خلفية دائرية
    icon_draw.ellipse([2, 2, icon_size-2, icon_size-2], fill=primary_color)
    
    # العين
    eye_bbox = [center - eye_radius, center - eye_radius, center + eye_radius, center + eye_radius]
    icon_draw.ellipse(eye_bbox, fill=white, outline=dark_blue, width=2)
    
    # البؤبؤ
    pupil_radius = icon_size // 6
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    icon_draw.ellipse(pupil_bbox, fill=dark_blue)
    
    # لصق الأيقونة
    image.paste(icon, (20, 20), icon)
    
    # إضافة النص
    try:
        font_large = ImageFont.truetype("arial.ttf", 32)
        font_small = ImageFont.truetype("arial.ttf", 16)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # النص الرئيسي
    text_x = icon_size + 40
    draw.text((text_x, 25), "EyeGuard AI", fill=dark_blue, font=font_large)
    draw.text((text_x, 65), "حماية عيونك بالذكاء الاصطناعي", fill=primary_color, font=font_small)
    
    # حفظ الشعار
    logo_path = os.path.join(output_dir, "eyeguard_logo.png")
    image.save(logo_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_logo.png")

def create_dark_variant(output_dir):
    """
    إنشاء نسخة للخلفية المظلمة
    """
    size = 256
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # ألوان للخلفية المظلمة
    primary_color = (100, 181, 246)    # أزرق فاتح
    secondary_color = (129, 199, 132)  # أخضر فاتح
    accent_color = (255, 213, 79)      # ذهبي فاتح
    white = (255, 255, 255)
    
    center = size // 2
    eye_radius = size // 3
    
    # خلفية دائرية متدرجة
    bg_radius = size // 2 - 4
    draw.ellipse([4, 4, size-4, size-4], fill=primary_color)
    
    # العين
    eye_bbox = [center - eye_radius, center - eye_radius, center + eye_radius, center + eye_radius]
    draw.ellipse(eye_bbox, fill=white, outline=accent_color, width=3)
    
    # القزحية
    iris_radius = size // 4
    iris_bbox = [center - iris_radius, center - iris_radius, center + iris_radius, center + iris_radius]
    draw.ellipse(iris_bbox, fill=secondary_color)
    
    # البؤبؤ
    pupil_radius = size // 6
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    draw.ellipse(pupil_bbox, fill=(30, 30, 30))
    
    # نقطة ضوء
    light_size = size // 32
    light_x = center - pupil_radius // 3
    light_y = center - pupil_radius // 3
    draw.ellipse([light_x - light_size, light_y - light_size, light_x + light_size, light_y + light_size], fill=white)
    
    # حفظ النسخة المظلمة
    dark_path = os.path.join(output_dir, "eyeguard_icon_dark.png")
    image.save(dark_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_icon_dark.png")

def create_simple_variant(output_dir):
    """
    إنشاء نسخة مبسطة
    """
    size = 128
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # ألوان مبسطة
    primary_color = (33, 150, 243)
    white = (255, 255, 255)
    
    center = size // 2
    
    # دائرة خارجية بسيطة
    outer_radius = size // 2 - 4
    draw.ellipse([4, 4, size-4, size-4], outline=primary_color, width=4)
    
    # العين المبسطة
    eye_width = size // 2
    eye_height = size // 4
    eye_bbox = [center - eye_width//2, center - eye_height//2, center + eye_width//2, center + eye_height//2]
    draw.ellipse(eye_bbox, fill=primary_color)
    
    # البؤبؤ
    pupil_radius = size // 8
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    draw.ellipse(pupil_bbox, fill=white)
    
    # حفظ النسخة المبسطة
    simple_path = os.path.join(output_dir, "eyeguard_icon_simple.png")
    image.save(simple_path, "PNG")
    print(f"✅ تم إنشاء: eyeguard_icon_simple.png")

def create_favicon():
    """
    إنشاء favicon للويب
    """
    icons_dir = "assets/icons"
    
    # إنشاء favicon بحجم 32x32
    size = 32
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # ألوان
    primary_color = (33, 150, 243)
    white = (255, 255, 255)
    
    center = size // 2
    
    # خلفية دائرية
    draw.ellipse([1, 1, size-1, size-1], fill=primary_color)
    
    # العين المبسطة
    eye_radius = size // 4
    eye_bbox = [center - eye_radius, center - eye_radius, center + eye_radius, center + eye_radius]
    draw.ellipse(eye_bbox, fill=white)
    
    # البؤبؤ
    pupil_radius = size // 8
    pupil_bbox = [center - pupil_radius, center - pupil_radius, center + pupil_radius, center + pupil_radius]
    draw.ellipse(pupil_bbox, fill=primary_color)
    
    # حفظ favicon
    favicon_path = os.path.join(icons_dir, "favicon.ico")
    image.save(favicon_path, format='ICO', sizes=[(32, 32)])
    print(f"✅ تم إنشاء: favicon.ico")

def main():
    """
    الدالة الرئيسية
    """
    print("🎨 بدء إنشاء أيقونات EyeGuard AI...")
    print("=" * 50)
    
    # إنشاء الأيقونة الرئيسية
    create_app_icon()
    
    # إنشاء متغيرات الشعار
    create_logo_variants()
    
    # إنشاء favicon
    create_favicon()
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء جميع الأيقونات بنجاح!")
    print("\nالملفات المُنشأة:")
    print("📁 assets/icons/")
    print("  ├── eyeguard_icon_16x16.png")
    print("  ├── eyeguard_icon_32x32.png") 
    print("  ├── eyeguard_icon_48x48.png")
    print("  ├── eyeguard_icon_64x64.png")
    print("  ├── eyeguard_icon_128x128.png")
    print("  ├── eyeguard_icon_256x256.png")
    print("  ├── eyeguard_icon_512x512.png")
    print("  ├── eyeguard_icon.ico")
    print("  ├── eyeguard_logo.png")
    print("  ├── eyeguard_icon_dark.png")
    print("  ├── eyeguard_icon_simple.png")
    print("  └── favicon.ico")

if __name__ == "__main__":
    main()

"""
ملف إعداد مشروع EyeGuard AI
Setup script for EyeGuard AI project
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# قراءة متطلبات المشروع
def read_requirements():
    requirements = []
    if os.path.exists("requirements.txt"):
        with open("requirements.txt", "r", encoding="utf-8") as fh:
            requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]
    return requirements

setup(
    name="eyeguard-ai",
    version="1.0.0",
    author="EyeGuard AI Team",
    author_email="<EMAIL>",
    description="تطبيق ذكي لتحليل صحة العين باستخدام الذكاء الاصطناعي",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/eyeguard-ai/eyeguard-ai",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "gpu": [
            "torch[cuda]>=1.9.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "eyeguard-ai=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml"],
        "assets": ["*"],
        "models": ["*.pth", "*.pt"],
    },
    keywords=[
        "artificial intelligence",
        "computer vision", 
        "medical imaging",
        "eye disease detection",
        "healthcare",
        "deep learning",
        "pytorch",
        "arabic",
        "الذكاء الاصطناعي",
        "تحليل العين",
        "الرعاية الصحية"
    ],
    project_urls={
        "Bug Reports": "https://github.com/eyeguard-ai/eyeguard-ai/issues",
        "Source": "https://github.com/eyeguard-ai/eyeguard-ai",
        "Documentation": "https://github.com/eyeguard-ai/eyeguard-ai/wiki",
    },
)

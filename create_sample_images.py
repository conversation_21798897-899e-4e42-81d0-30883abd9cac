"""
إنشاء صور اختبار لتطبيق EyeGuard AI
Create sample images for EyeGuard AI testing
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_images():
    """
    إنشاء صور اختبار للحالات المختلفة
    """
    # إنشاء مجلد الصور إذا لم يكن موجوداً
    sample_dir = "assets/sample_images"
    os.makedirs(sample_dir, exist_ok=True)
    
    # أحجام الصور
    size = (300, 200)
    
    # الألوان للحالات المختلفة
    samples = [
        {
            "name": "normal_eye_sample.jpg",
            "color": (255, 255, 255),  # أبيض للعين الطبيعية
            "text": "عين طبيعية",
            "text_color": (0, 0, 0)
        },
        {
            "name": "red_eye_sample.jpg", 
            "color": (255, 200, 200),  # أحمر فاتح للاحمرار
            "text": "احمرار العين",
            "text_color": (150, 0, 0)
        },
        {
            "name": "cataract_eye_sample.jpg",
            "color": (240, 240, 240),  # رمادي فاتح للمياه البيضاء
            "text": "مياه بيضاء",
            "text_color": (100, 100, 100)
        },
        {
            "name": "retinal_eye_sample.jpg",
            "color": (255, 220, 180),  # بيج لمشاكل الشبكية
            "text": "مشاكل الشبكية",
            "text_color": (120, 80, 40)
        },
        {
            "name": "dry_eye_sample.jpg",
            "color": (200, 220, 255),  # أزرق فاتح للجفاف
            "text": "جفاف العين",
            "text_color": (0, 50, 150)
        }
    ]
    
    print("🖼️ إنشاء صور الاختبار...")
    
    for sample in samples:
        try:
            # إنشاء صورة بلون خلفية
            image = Image.new('RGB', size, sample["color"])
            draw = ImageDraw.Draw(image)
            
            # رسم دائرة تمثل العين
            eye_center = (size[0] // 2, size[1] // 2)
            eye_radius = min(size) // 4
            
            # رسم العين الخارجية
            eye_bbox = [
                eye_center[0] - eye_radius,
                eye_center[1] - eye_radius,
                eye_center[0] + eye_radius,
                eye_center[1] + eye_radius
            ]
            draw.ellipse(eye_bbox, fill=(200, 200, 200), outline=(100, 100, 100), width=2)
            
            # رسم البؤبؤ
            pupil_radius = eye_radius // 3
            pupil_bbox = [
                eye_center[0] - pupil_radius,
                eye_center[1] - pupil_radius,
                eye_center[0] + pupil_radius,
                eye_center[1] + pupil_radius
            ]
            draw.ellipse(pupil_bbox, fill=(50, 50, 50))
            
            # إضافة النص
            try:
                # محاولة استخدام خط عربي
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            # حساب موقع النص
            text_bbox = draw.textbbox((0, 0), sample["text"], font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (size[0] - text_width) // 2
            text_y = size[1] - 30
            
            # رسم النص
            draw.text((text_x, text_y), sample["text"], fill=sample["text_color"], font=font)
            
            # حفظ الصورة
            file_path = os.path.join(sample_dir, sample["name"])
            image.save(file_path, "JPEG", quality=95)
            
            print(f"✅ تم إنشاء: {sample['name']}")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء {sample['name']}: {e}")
    
    # إنشاء ملف README للصور
    readme_content = """# صور الاختبار - EyeGuard AI

هذه الصور مخصصة لاختبار تطبيق EyeGuard AI.

## الصور المتوفرة:

1. **normal_eye_sample.jpg** - عين طبيعية
2. **red_eye_sample.jpg** - احمرار والتهاب
3. **cataract_eye_sample.jpg** - مياه بيضاء
4. **retinal_eye_sample.jpg** - مشاكل الشبكية  
5. **dry_eye_sample.jpg** - جفاف العين

## كيفية الاستخدام:

1. افتح تطبيق EyeGuard AI
2. اضغط على "اختيار صورة"
3. اختر إحدى هذه الصور
4. اضغط على "تحليل الصورة"
5. شاهد النتائج والتوصيات

⚠️ **تنبيه**: هذه صور اختبار فقط وليست صور طبية حقيقية.
"""
    
    readme_path = os.path.join(sample_dir, "README.md")
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ تم إنشاء ملف README في: {readme_path}")
    print("🎉 تم إنشاء جميع صور الاختبار بنجاح!")

if __name__ == "__main__":
    create_sample_images()

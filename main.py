#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
👁️ EyeGuard AI - حماية عيونك بالذكاء الاصطناعي
الملف الرئيسي لتشغيل التطبيق

EyeGuard AI - Protect Your Eyes with Artificial Intelligence
Main application entry point
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة مسار المشروع للاستيراد
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """
    فحص المكتبات المطلوبة
    """
    required_packages = [
        ('torch', 'PyTorch'),
        ('PIL', 'Pillow'),
        ('numpy', 'NumPy'),
        ('pyttsx3', 'pyttsx3')
    ]
    
    missing_packages = []
    
    for package, display_name in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(display_name)
    
    if missing_packages:
        error_msg = f"""
المكتبات التالية مفقودة:
{', '.join(missing_packages)}

لتثبيت المكتبات المطلوبة، قم بتشغيل:
pip install -r requirements.txt

أو قم بتثبيت كل مكتبة على حدة:
pip install torch torchvision pillow numpy pyttsx3
        """
        
        # عرض رسالة خطأ
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("مكتبات مفقودة", error_msg)
        root.destroy()
        
        return False
    
    return True

def setup_environment():
    """
    إعداد البيئة والمجلدات المطلوبة
    """
    # إنشاء المجلدات المطلوبة إذا لم تكن موجودة
    required_dirs = [
        'assets/icons',
        'assets/sounds',
        'assets/sample_images',
        'models',
        'gui/components',
        'gui/themes',
        'tests'
    ]
    
    for dir_path in required_dirs:
        full_path = os.path.join(current_dir, dir_path)
        if not os.path.exists(full_path):
            try:
                os.makedirs(full_path, exist_ok=True)
                print(f"✅ تم إنشاء المجلد: {dir_path}")
            except Exception as e:
                print(f"❌ فشل في إنشاء المجلد {dir_path}: {e}")

def main():
    """
    الدالة الرئيسية لتشغيل التطبيق
    """
    print("🚀 بدء تشغيل EyeGuard AI...")
    
    # فحص المكتبات المطلوبة
    print("🔍 فحص المكتبات المطلوبة...")
    if not check_dependencies():
        print("❌ فشل في فحص المكتبات")
        return 1
    
    print("✅ جميع المكتبات متوفرة")
    
    # إعداد البيئة
    print("⚙️ إعداد البيئة...")
    setup_environment()
    
    try:
        # استيراد وتشغيل التطبيق
        print("📱 تحميل واجهة المستخدم...")
        from gui.main_window import EyeGuardMainWindow
        
        print("🎉 تم تحميل التطبيق بنجاح!")
        print("👁️ مرحباً بك في EyeGuard AI")
        print("=" * 50)
        
        # إنشاء وتشغيل التطبيق
        app = EyeGuardMainWindow()
        app.run()
        
        print("👋 تم إغلاق التطبيق")
        return 0
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الوحدات:
{str(e)}

تأكد من أن جميع الملفات موجودة في المكان الصحيح:
- gui/main_window.py
- models/eye_classifier.py
- utils/image_processor.py
- utils/audio_handler.py
- utils/treatment_guide.py
        """
        
        print(f"❌ {error_msg}")
        
        # عرض رسالة خطأ للمستخدم
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التطبيق", error_msg)
        root.destroy()
        
        return 1
        
    except Exception as e:
        error_msg = f"""
حدث خطأ غير متوقع:
{str(e)}

تفاصيل الخطأ:
{traceback.format_exc()}
        """
        
        print(f"❌ {error_msg}")
        
        # عرض رسالة خطأ للمستخدم
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ غير متوقع", f"حدث خطأ غير متوقع:\n{str(e)}")
        root.destroy()
        
        return 1

def show_help():
    """
    عرض تعليمات الاستخدام
    """
    help_text = """
👁️ EyeGuard AI - حماية عيونك بالذكاء الاصطناعي

الاستخدام:
    python main.py              تشغيل التطبيق
    python main.py --help       عرض هذه التعليمات
    python main.py --version    عرض إصدار التطبيق
    python main.py --test       اختبار المكونات

الوصف:
    تطبيق ذكي لتحليل صور العين واكتشاف المشاكل الشائعة
    باستخدام تقنيات الذكاء الاصطناعي والتعلم العميق.

الحالات المدعومة:
    • العين الطبيعية
    • الاحمرار والالتهاب
    • المياه البيضاء
    • مشاكل الشبكية
    • جفاف العين

المتطلبات:
    • Python 3.8+
    • PyTorch
    • Pillow (PIL)
    • NumPy
    • pyttsx3
    • tkinter

للمزيد من المعلومات، راجع ملف README.md
    """
    print(help_text)

def show_version():
    """
    عرض معلومات الإصدار
    """
    version_info = """
👁️ EyeGuard AI
الإصدار: 1.0.0
تاريخ الإصدار: 2024
المطور: فريق EyeGuard AI
الترخيص: MIT License
الموقع: https://github.com/eyeguard-ai

Python: {python_version}
النظام: {platform}
    """.format(
        python_version=sys.version.split()[0],
        platform=sys.platform
    )
    print(version_info)

def test_components():
    """
    اختبار مكونات التطبيق
    """
    print("🧪 اختبار مكونات التطبيق...")
    
    tests = [
        ("فحص المكتبات", check_dependencies),
        ("إعداد البيئة", setup_environment),
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"⏳ {test_name}...")
            result = test_func()
            if result is False:
                print(f"❌ فشل: {test_name}")
            else:
                print(f"✅ نجح: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    # اختبار استيراد الوحدات
    modules_to_test = [
        'models.eye_classifier',
        'utils.image_processor',
        'utils.audio_handler',
        'utils.treatment_guide'
    ]
    
    for module_name in modules_to_test:
        try:
            print(f"⏳ اختبار استيراد {module_name}...")
            __import__(module_name)
            print(f"✅ نجح استيراد {module_name}")
        except Exception as e:
            print(f"❌ فشل استيراد {module_name}: {e}")
    
    print("🏁 انتهى الاختبار")

if __name__ == "__main__":
    # معالجة معاملات سطر الأوامر
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif arg in ['--version', '-v', 'version']:
            show_version()
            sys.exit(0)
        elif arg in ['--test', '-t', 'test']:
            test_components()
            sys.exit(0)
        else:
            print(f"معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help لعرض التعليمات")
            sys.exit(1)
    
    # تشغيل التطبيق
    exit_code = main()
    sys.exit(exit_code)

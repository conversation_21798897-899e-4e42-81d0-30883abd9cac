"""
ملف إعدادات تطبيق EyeGuard AI
Configuration file for EyeGuard AI application
"""

import os
from pathlib import Path

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent
ASSETS_DIR = PROJECT_ROOT / "assets"
MODELS_DIR = PROJECT_ROOT / "models"
UTILS_DIR = PROJECT_ROOT / "utils"
GUI_DIR = PROJECT_ROOT / "gui"
TESTS_DIR = PROJECT_ROOT / "tests"

# مسارات الأصول
ICONS_DIR = ASSETS_DIR / "icons"
SOUNDS_DIR = ASSETS_DIR / "sounds"
SAMPLE_IMAGES_DIR = ASSETS_DIR / "sample_images"

# إعدادات النموذج
MODEL_CONFIG = {
    "input_size": (224, 224),
    "num_classes": 5,
    "model_name": "eye_classifier",
    "model_file": "trained_model.pth",
    "confidence_threshold": 0.6,
    "class_names": [
        "طبيعي",           # Normal
        "احمرار والتهاب",   # Red/Inflammation  
        "مياه بيضاء",       # Cataract
        "مشاكل الشبكية",    # Retinal Issues
        "جفاف العين"        # Dry Eye
    ]
}

# إعدادات معالجة الصور
IMAGE_CONFIG = {
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
    "max_file_size_mb": 10,
    "resize_dimensions": (224, 224),
    "enhancement_settings": {
        "brightness": 1.1,
        "contrast": 1.2,
        "sharpness": 1.1
    }
}

# إعدادات الصوت
AUDIO_CONFIG = {
    "speech_rate": 150,  # كلمة في الدقيقة
    "volume": 0.8,       # من 0.0 إلى 1.0
    "voice_preference": ["arabic", "female"],  # تفضيلات الصوت
    "enable_audio": True,
    "async_speech": True
}

# إعدادات واجهة المستخدم
GUI_CONFIG = {
    "window_title": "👁️ EyeGuard AI - حماية عيونك بالذكاء الاصطناعي",
    "window_size": (1000, 700),
    "min_window_size": (800, 600),
    "default_theme": "light",  # light أو dark
    "font_family": "Arial",
    "font_sizes": {
        "title": 24,
        "subtitle": 12,
        "button": 12,
        "text": 11,
        "small": 10
    },
    "colors": {
        "light": {
            "bg": "#f0f0f0",
            "fg": "#000000",
            "button_bg": "#e1e1e1",
            "button_fg": "#000000",
            "entry_bg": "#ffffff",
            "entry_fg": "#000000",
            "frame_bg": "#ffffff"
        },
        "dark": {
            "bg": "#2b2b2b",
            "fg": "#ffffff",
            "button_bg": "#404040",
            "button_fg": "#ffffff",
            "entry_bg": "#404040",
            "entry_fg": "#ffffff",
            "frame_bg": "#353535"
        }
    },
    "button_colors": {
        "select": "#4CAF50",      # أخضر
        "analyze": "#2196F3",     # أزرق
        "speak": "#FF9800",       # برتقالي
        "save": "#9C27B0",        # بنفسجي
        "info": "#607D8B"         # رمادي مزرق
    }
}

# إعدادات العلاج والنصائح
TREATMENT_CONFIG = {
    "urgency_levels": {
        "منخفض": "#4CAF50",      # أخضر
        "متوسط": "#FF9800",       # برتقالي
        "عالي": "#F44336",        # أحمر
        "عالي جداً": "#D32F2F"    # أحمر داكن
    },
    "emergency_contacts": {
        "طوارئ عام": "123",
        "الإسعاف": "997",
        "مستشفى الملك فيصل التخصصي": "011-464-7272",
        "مستشفى الملك خالد للعيون": "011-482-1234"
    }
}

# إعدادات التطبيق العامة
APP_CONFIG = {
    "app_name": "EyeGuard AI",
    "version": "1.0.0",
    "author": "EyeGuard AI Team",
    "license": "MIT",
    "description": "تطبيق ذكي لتحليل صحة العين باستخدام الذكاء الاصطناعي",
    "website": "https://github.com/eyeguard-ai",
    "support_email": "<EMAIL>",
    "language": "ar",  # ar للعربية، en للإنجليزية
    "auto_save_results": True,
    "show_welcome_message": True,
    "enable_analytics": False,  # لحماية الخصوصية
    "debug_mode": False
}

# إعدادات الأمان والخصوصية
SECURITY_CONFIG = {
    "encrypt_saved_data": False,  # تشفير البيانات المحفوظة
    "delete_temp_files": True,    # حذف الملفات المؤقتة
    "log_user_actions": False,    # تسجيل أفعال المستخدم
    "share_anonymous_stats": False,  # مشاركة إحصائيات مجهولة
    "require_consent": True,      # طلب موافقة المستخدم
    "data_retention_days": 0      # عدد أيام الاحتفاظ بالبيانات (0 = لا احتفاظ)
}

# إعدادات التطوير والاختبار
DEV_CONFIG = {
    "enable_debug_logs": False,
    "show_performance_metrics": False,
    "enable_test_mode": False,
    "mock_ai_predictions": False,  # استخدام تنبؤات وهمية للاختبار
    "log_level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "profiling_enabled": False
}

# دوال مساعدة للإعدادات
def get_model_path():
    """الحصول على مسار ملف النموذج"""
    return MODELS_DIR / MODEL_CONFIG["model_file"]

def get_config_value(section, key, default=None):
    """الحصول على قيمة إعداد معينة"""
    configs = {
        "model": MODEL_CONFIG,
        "image": IMAGE_CONFIG,
        "audio": AUDIO_CONFIG,
        "gui": GUI_CONFIG,
        "treatment": TREATMENT_CONFIG,
        "app": APP_CONFIG,
        "security": SECURITY_CONFIG,
        "dev": DEV_CONFIG
    }
    
    return configs.get(section, {}).get(key, default)

def is_debug_mode():
    """التحقق من تفعيل وضع التطوير"""
    return APP_CONFIG.get("debug_mode", False) or DEV_CONFIG.get("enable_debug_logs", False)

def get_supported_languages():
    """الحصول على قائمة اللغات المدعومة"""
    return ["ar", "en"]

def validate_config():
    """التحقق من صحة الإعدادات"""
    errors = []
    
    # التحقق من وجود المجلدات المطلوبة
    required_dirs = [ASSETS_DIR, MODELS_DIR, UTILS_DIR, GUI_DIR]
    for dir_path in required_dirs:
        if not dir_path.exists():
            errors.append(f"المجلد المطلوب غير موجود: {dir_path}")
    
    # التحقق من صحة إعدادات النموذج
    if MODEL_CONFIG["num_classes"] != len(MODEL_CONFIG["class_names"]):
        errors.append("عدد الفئات لا يتطابق مع أسماء الفئات")
    
    # التحقق من صحة إعدادات الصوت
    if not (0.0 <= AUDIO_CONFIG["volume"] <= 1.0):
        errors.append("مستوى الصوت يجب أن يكون بين 0.0 و 1.0")
    
    return errors

# تحديث الإعدادات من متغيرات البيئة
def load_env_config():
    """تحميل الإعدادات من متغيرات البيئة"""
    
    # إعدادات التطبيق
    if os.getenv("EYEGUARD_DEBUG"):
        APP_CONFIG["debug_mode"] = os.getenv("EYEGUARD_DEBUG").lower() == "true"
    
    if os.getenv("EYEGUARD_LANGUAGE"):
        APP_CONFIG["language"] = os.getenv("EYEGUARD_LANGUAGE")
    
    # إعدادات الصوت
    if os.getenv("EYEGUARD_AUDIO_ENABLED"):
        AUDIO_CONFIG["enable_audio"] = os.getenv("EYEGUARD_AUDIO_ENABLED").lower() == "true"
    
    # إعدادات الأمان
    if os.getenv("EYEGUARD_ANALYTICS"):
        SECURITY_CONFIG["share_anonymous_stats"] = os.getenv("EYEGUARD_ANALYTICS").lower() == "true"

# تحميل الإعدادات من متغيرات البيئة عند الاستيراد
load_env_config()

# التحقق من صحة الإعدادات
if __name__ == "__main__":
    config_errors = validate_config()
    if config_errors:
        print("❌ أخطاء في الإعدادات:")
        for error in config_errors:
            print(f"  - {error}")
    else:
        print("✅ جميع الإعدادات صحيحة")

"""
دليل العلاج والنصائح الطبية لتطبيق EyeGuard AI
Treatment Guide and Medical Advice for EyeGuard AI
"""

class TreatmentGuide:
    """
    فئة دليل العلاج والنصائح الطبية
    """
    
    def __init__(self):
        self.treatments = {
            'طبيعي': {
                'title': 'العين طبيعية - الحمد لله',
                'description': 'العين تبدو في حالة طبيعية وصحية',
                'recommendations': [
                    '✅ حافظ على نظافة العين',
                    '✅ استخدم نظارات شمسية عند التعرض للشمس',
                    '✅ تناول الأطعمة الغنية بفيتامين A',
                    '✅ خذ فترات راحة عند استخدام الشاشات',
                    '✅ اشرب كمية كافية من الماء'
                ],
                'when_to_see_doctor': 'في حالة ظهور أي أعراض جديدة',
                'urgency': 'منخفض',
                'color': '#4CAF50'  # أخضر
            },
            
            'احمرار والتهاب': {
                'title': 'احمرار والتهاب في العين',
                'description': 'يوجد احمرار أو التهاب قد يكون بسبب عدوى أو تهيج',
                'recommendations': [
                    '🧼 اغسل يديك قبل لمس العين',
                    '💧 استخدم كمادات باردة لمدة 10-15 دقيقة',
                    '💊 تجنب فرك العين',
                    '🚫 تجنب استخدام العدسات اللاصقة',
                    '💧 استخدم قطرات العين المرطبة',
                    '😴 احصل على راحة كافية'
                ],
                'when_to_see_doctor': 'إذا استمر الاحمرار أكثر من 2-3 أيام أو ازداد سوءاً',
                'urgency': 'متوسط',
                'color': '#FF9800'  # برتقالي
            },
            
            'مياه بيضاء': {
                'title': 'اشتباه في المياه البيضاء',
                'description': 'قد يكون هناك عتامة في عدسة العين (المياه البيضاء)',
                'recommendations': [
                    '👨‍⚕️ راجع طبيب العيون فوراً للتشخيص الدقيق',
                    '🕶️ استخدم نظارات شمسية لحماية العين',
                    '💡 تجنب القيادة في الليل إذا كانت الرؤية ضبابية',
                    '💊 تناول مضادات الأكسدة (فيتامين C و E)',
                    '🚭 تجنب التدخين',
                    '🍎 تناول الأطعمة الغنية بمضادات الأكسدة'
                ],
                'when_to_see_doctor': 'فوراً - المياه البيضاء تحتاج تدخل طبي',
                'urgency': 'عالي',
                'color': '#F44336'  # أحمر
            },
            
            'مشاكل الشبكية': {
                'title': 'مشاكل محتملة في الشبكية',
                'description': 'قد يكون هناك مشاكل في شبكية العين تحتاج فحص طبي',
                'recommendations': [
                    '🚨 راجع طبيب العيون المختص فوراً',
                    '📱 تجنب الاستخدام المفرط للشاشات',
                    '🍇 تناول الأطعمة الغنية بالأنثوسيانين (التوت الأزرق)',
                    '🐟 تناول الأسماك الغنية بأوميجا 3',
                    '💊 تحكم في مستوى السكر إذا كنت مصاباً بالسكري',
                    '🩺 افحص ضغط الدم بانتظام'
                ],
                'when_to_see_doctor': 'فوراً - مشاكل الشبكية خطيرة',
                'urgency': 'عالي جداً',
                'color': '#D32F2F'  # أحمر داكن
            },
            
            'جفاف العين': {
                'title': 'جفاف في العين',
                'description': 'العين تعاني من نقص في الترطيب الطبيعي',
                'recommendations': [
                    '💧 استخدم قطرات العين المرطبة بانتظام',
                    '💨 تجنب التعرض المباشر للمراوح أو المكيفات',
                    '💻 اتبع قاعدة 20-20-20 (كل 20 دقيقة انظر لشيء على بعد 20 قدم لمدة 20 ثانية)',
                    '💧 اشرب كمية كافية من الماء',
                    '🌿 استخدم مرطب الهواء في الغرفة',
                    '😴 احصل على نوم كافي'
                ],
                'when_to_see_doctor': 'إذا استمر الجفاف رغم العلاج المنزلي',
                'urgency': 'منخفض إلى متوسط',
                'color': '#2196F3'  # أزرق
            }
        }
        
        # نصائح عامة لصحة العين
        self.general_tips = [
            '👀 افحص عينيك بانتظام عند طبيب العيون',
            '🥕 تناول الأطعمة المفيدة للعين (الجزر، السبانخ، السمك)',
            '🕶️ احم عينيك من الأشعة فوق البنفسجية',
            '💻 خذ فترات راحة عند استخدام الشاشات',
            '🚭 تجنب التدخين',
            '💧 حافظ على ترطيب العين',
            '😴 احصل على نوم كافي',
            '🧼 اغسل يديك قبل لمس العين'
        ]
    
    def get_treatment_info(self, class_name):
        """
        الحصول على معلومات العلاج لحالة معينة
        """
        return self.treatments.get(class_name, {
            'title': 'حالة غير معروفة',
            'description': 'لم يتم التعرف على الحالة',
            'recommendations': ['راجع طبيب العيون للتشخيص الصحيح'],
            'when_to_see_doctor': 'في أقرب وقت ممكن',
            'urgency': 'متوسط',
            'color': '#9E9E9E'
        })
    
    def get_urgency_level(self, class_name):
        """
        الحصول على مستوى الإلحاح للحالة
        """
        treatment_info = self.get_treatment_info(class_name)
        return treatment_info.get('urgency', 'متوسط')
    
    def get_color_code(self, class_name):
        """
        الحصول على رمز اللون للحالة
        """
        treatment_info = self.get_treatment_info(class_name)
        return treatment_info.get('color', '#9E9E9E')
    
    def format_treatment_text(self, class_name):
        """
        تنسيق نص العلاج للعرض
        """
        info = self.get_treatment_info(class_name)
        
        text = f"📋 {info['title']}\n\n"
        text += f"📝 الوصف:\n{info['description']}\n\n"
        text += "💡 التوصيات:\n"
        
        for recommendation in info['recommendations']:
            text += f"   {recommendation}\n"
        
        text += f"\n🏥 متى تراجع الطبيب:\n{info['when_to_see_doctor']}\n\n"
        text += f"⚠️ مستوى الإلحاح: {info['urgency']}"
        
        return text
    
    def get_emergency_contacts(self):
        """
        أرقام الطوارئ الطبية
        """
        return {
            'طوارئ عام': '123',
            'الإسعاف': '997',
            'مستشفى الملك فيصل التخصصي': '011-464-7272',
            'مستشفى الملك خالد للعيون': '011-482-1234'
        }
    
    def get_prevention_tips(self):
        """
        نصائح الوقاية من أمراض العين
        """
        return {
            'نصائح يومية': [
                'اغسل يديك قبل لمس العين',
                'تجنب فرك العين بقوة',
                'استخدم نظارات شمسية عالية الجودة',
                'خذ فترات راحة من الشاشات'
            ],
            'التغذية': [
                'تناول الأطعمة الغنية بفيتامين A',
                'أكل السمك مرتين في الأسبوع',
                'تناول الخضروات الورقية الخضراء',
                'شرب كمية كافية من الماء'
            ],
            'نمط الحياة': [
                'تجنب التدخين',
                'ممارسة الرياضة بانتظام',
                'الحصول على نوم كافي',
                'إدارة التوتر والضغط النفسي'
            ]
        }

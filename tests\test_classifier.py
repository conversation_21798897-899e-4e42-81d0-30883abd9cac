"""
اختبارات نموذج تصنيف أمراض العين
Tests for Eye Disease Classification Model
"""

import unittest
import sys
import os
import tempfile
from PIL import Image
import numpy as np

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.eye_classifier import SimpleEyeClassifier, load_model
from utils.image_processor import ImageProcessor
from utils.audio_handler import AudioHandler
from utils.treatment_guide import TreatmentGuide

class TestEyeClassifier(unittest.TestCase):
    """
    اختبارات نموذج تصنيف العين
    """
    
    def setUp(self):
        """
        إعداد الاختبارات
        """
        self.classifier = SimpleEyeClassifier()
        self.test_image_path = self.create_test_image()
    
    def create_test_image(self):
        """
        إنشاء صورة اختبار مؤقتة
        """
        # إنشاء صورة RGB بسيطة
        image = Image.new('RGB', (224, 224), color='white')
        
        # حفظ في ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
        image.save(temp_file.name)
        temp_file.close()
        
        return temp_file.name
    
    def tearDown(self):
        """
        تنظيف بعد الاختبارات
        """
        # حذف الصورة المؤقتة
        if os.path.exists(self.test_image_path):
            os.unlink(self.test_image_path)
    
    def test_classifier_initialization(self):
        """
        اختبار تهيئة المصنف
        """
        self.assertIsNotNone(self.classifier)
        self.assertEqual(len(self.classifier.class_names), 5)
        self.assertIn('طبيعي', self.classifier.class_names)
    
    def test_prediction_structure(self):
        """
        اختبار هيكل نتيجة التنبؤ
        """
        prediction = self.classifier.predict(self.test_image_path)
        
        # التحقق من وجود المفاتيح المطلوبة
        required_keys = ['class_id', 'class_name', 'confidence', 'probabilities']
        for key in required_keys:
            self.assertIn(key, prediction)
        
        # التحقق من نوع البيانات
        self.assertIsInstance(prediction['class_id'], int)
        self.assertIsInstance(prediction['class_name'], str)
        self.assertIsInstance(prediction['confidence'], float)
        self.assertIsInstance(prediction['probabilities'], list)
        
        # التحقق من النطاقات
        self.assertGreaterEqual(prediction['class_id'], 0)
        self.assertLess(prediction['class_id'], 5)
        self.assertGreaterEqual(prediction['confidence'], 0.0)
        self.assertLessEqual(prediction['confidence'], 1.0)
        self.assertEqual(len(prediction['probabilities']), 5)
    
    def test_class_names(self):
        """
        اختبار أسماء الفئات
        """
        expected_classes = ['طبيعي', 'احمرار والتهاب', 'مياه بيضاء', 'مشاكل الشبكية', 'جفاف العين']
        self.assertEqual(self.classifier.class_names, expected_classes)
    
    def test_load_model_function(self):
        """
        اختبار دالة تحميل النموذج
        """
        model = load_model()
        self.assertIsNotNone(model)
        
        # اختبار التنبؤ
        prediction = model.predict(self.test_image_path)
        self.assertIsInstance(prediction, dict)

class TestImageProcessor(unittest.TestCase):
    """
    اختبارات معالج الصور
    """
    
    def setUp(self):
        """
        إعداد الاختبارات
        """
        self.processor = ImageProcessor()
        self.test_image_path = self.create_test_image()
    
    def create_test_image(self):
        """
        إنشاء صورة اختبار
        """
        image = Image.new('RGB', (300, 200), color='blue')
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        image.save(temp_file.name)
        temp_file.close()
        return temp_file.name
    
    def tearDown(self):
        """
        تنظيف بعد الاختبارات
        """
        if os.path.exists(self.test_image_path):
            os.unlink(self.test_image_path)
    
    def test_is_valid_image(self):
        """
        اختبار التحقق من صحة الصورة
        """
        # صورة صحيحة
        self.assertTrue(self.processor.is_valid_image(self.test_image_path))
        
        # ملف غير موجود
        self.assertFalse(self.processor.is_valid_image('nonexistent.jpg'))
    
    def test_get_image_info(self):
        """
        اختبار الحصول على معلومات الصورة
        """
        info = self.processor.get_image_info(self.test_image_path)
        
        self.assertIsNotNone(info)
        self.assertIn('width', info)
        self.assertIn('height', info)
        self.assertIn('format', info)
        self.assertEqual(info['width'], 300)
        self.assertEqual(info['height'], 200)
    
    def test_resize_image(self):
        """
        اختبار تغيير حجم الصورة
        """
        resized = self.processor.resize_image(self.test_image_path, (100, 100))
        
        self.assertIsNotNone(resized)
        self.assertEqual(resized.size, (100, 100))

class TestAudioHandler(unittest.TestCase):
    """
    اختبارات معالج الصوت
    """
    
    def setUp(self):
        """
        إعداد الاختبارات
        """
        self.audio_handler = AudioHandler()
    
    def test_audio_handler_initialization(self):
        """
        اختبار تهيئة معالج الصوت
        """
        self.assertIsNotNone(self.audio_handler)
        # قد يكون المحرك None إذا لم يكن pyttsx3 متاحاً
    
    def test_speak_diagnosis(self):
        """
        اختبار قراءة التشخيص
        """
        prediction = {
            'class_name': 'طبيعي',
            'confidence': 0.95
        }
        
        # يجب ألا يحدث خطأ
        try:
            result = self.audio_handler.speak_diagnosis(prediction)
            # النتيجة قد تكون True أو False حسب توفر المحرك
            self.assertIsInstance(result, bool)
        except Exception as e:
            # إذا حدث خطأ، يجب أن يكون بسبب عدم توفر المحرك
            self.assertIn('محرك الصوت', str(e))

class TestTreatmentGuide(unittest.TestCase):
    """
    اختبارات دليل العلاج
    """
    
    def setUp(self):
        """
        إعداد الاختبارات
        """
        self.guide = TreatmentGuide()
    
    def test_treatment_guide_initialization(self):
        """
        اختبار تهيئة دليل العلاج
        """
        self.assertIsNotNone(self.guide)
        self.assertIsInstance(self.guide.treatments, dict)
        self.assertGreater(len(self.guide.treatments), 0)
    
    def test_get_treatment_info(self):
        """
        اختبار الحصول على معلومات العلاج
        """
        # حالة موجودة
        info = self.guide.get_treatment_info('طبيعي')
        self.assertIsInstance(info, dict)
        self.assertIn('title', info)
        self.assertIn('recommendations', info)
        
        # حالة غير موجودة
        info = self.guide.get_treatment_info('حالة غير موجودة')
        self.assertIsInstance(info, dict)
        self.assertEqual(info['title'], 'حالة غير معروفة')
    
    def test_urgency_levels(self):
        """
        اختبار مستويات الإلحاح
        """
        urgency = self.guide.get_urgency_level('طبيعي')
        self.assertIsInstance(urgency, str)
        
        urgency = self.guide.get_urgency_level('مياه بيضاء')
        self.assertEqual(urgency, 'عالي')
    
    def test_color_codes(self):
        """
        اختبار رموز الألوان
        """
        color = self.guide.get_color_code('طبيعي')
        self.assertIsInstance(color, str)
        self.assertTrue(color.startswith('#'))
    
    def test_format_treatment_text(self):
        """
        اختبار تنسيق نص العلاج
        """
        text = self.guide.format_treatment_text('طبيعي')
        self.assertIsInstance(text, str)
        self.assertIn('طبيعي', text)
        self.assertIn('التوصيات', text)

def run_all_tests():
    """
    تشغيل جميع الاختبارات
    """
    print("🧪 بدء تشغيل الاختبارات...")
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات المصنف
    test_suite.addTest(unittest.makeSuite(TestEyeClassifier))
    test_suite.addTest(unittest.makeSuite(TestImageProcessor))
    test_suite.addTest(unittest.makeSuite(TestAudioHandler))
    test_suite.addTest(unittest.makeSuite(TestTreatmentGuide))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        return True
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"❌ خطأ في {len(result.errors)} اختبار")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

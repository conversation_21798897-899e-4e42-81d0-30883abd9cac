"""
الواجهة الرئيسية لتطبيق EyeGuard AI
Main GUI Window for EyeGuard AI
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import os
import sys
import threading

# إضافة مسار المشروع للاستيراد
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.eye_classifier import load_model
from utils.image_processor import ImageProcessor
from utils.audio_handler import AudioHandler
from utils.treatment_guide import TreatmentGuide

class EyeGuardMainWindow:
    """
    النافذة الرئيسية لتطبيق EyeGuard AI
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.load_components()
        self.create_widgets()
        self.setup_layout()
        
    def setup_window(self):
        """
        إعداد النافذة الرئيسية
        """
        self.root.title("👁️ EyeGuard AI - حماية عيونك بالذكاء الاصطناعي")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # تعيين أيقونة التطبيق
        try:
            icon_path = "assets/icons/eyeguard_app.ico"
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
            else:
                # محاولة استخدام أيقونة PNG
                icon_png_path = "assets/icons/eyeguard_icon_32x32.png"
                if os.path.exists(icon_png_path):
                    icon_image = tk.PhotoImage(file=icon_png_path)
                    self.root.iconphoto(True, icon_image)
        except Exception as e:
            print(f"تعذر تحميل الأيقونة: {e}")
        
        # إعداد الثيم
        self.is_dark_mode = False
        self.setup_theme()
    
    def setup_variables(self):
        """
        إعداد المتغيرات
        """
        self.current_image_path = None
        self.current_prediction = None
        self.processed_image = None
        
    def load_components(self):
        """
        تحميل المكونات الأساسية
        """
        try:
            self.model = load_model()
            self.image_processor = ImageProcessor()
            self.audio_handler = AudioHandler()
            self.treatment_guide = TreatmentGuide()
            print("✅ تم تحميل جميع المكونات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المكونات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل المكونات: {e}")
    
    def setup_theme(self):
        """
        إعداد ثيم التطبيق
        """
        if self.is_dark_mode:
            self.colors = {
                'bg': '#2b2b2b',
                'fg': '#ffffff',
                'button_bg': '#404040',
                'button_fg': '#ffffff',
                'entry_bg': '#404040',
                'entry_fg': '#ffffff',
                'frame_bg': '#353535'
            }
        else:
            self.colors = {
                'bg': '#f0f0f0',
                'fg': '#000000',
                'button_bg': '#e1e1e1',
                'button_fg': '#000000',
                'entry_bg': '#ffffff',
                'entry_fg': '#000000',
                'frame_bg': '#ffffff'
            }
        
        self.root.configure(bg=self.colors['bg'])
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        """
        # إطار العنوان
        self.create_header_frame()
        
        # إطار الصورة
        self.create_image_frame()
        
        # إطار الأزرار
        self.create_buttons_frame()
        
        # إطار النتائج
        self.create_results_frame()
        
        # إطار الحالة
        self.create_status_frame()
    
    def create_header_frame(self):
        """
        إنشاء إطار العنوان
        """
        self.header_frame = tk.Frame(self.root, bg=self.colors['bg'], height=80)
        self.header_frame.pack(fill='x', padx=10, pady=5)
        self.header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            self.header_frame,
            text="👁️ EyeGuard AI",
            font=("Arial", 24, "bold"),
            bg=self.colors['bg'],
            fg='#2196F3'
        )
        title_label.pack(side='top', pady=5)
        
        # وصف التطبيق
        subtitle_label = tk.Label(
            self.header_frame,
            text="حماية عيونك بالذكاء الاصطناعي - تحليل ذكي لصحة العين",
            font=("Arial", 12),
            bg=self.colors['bg'],
            fg=self.colors['fg']
        )
        subtitle_label.pack(side='top')
        
        # زر الوضع المظلم
        self.dark_mode_btn = tk.Button(
            self.header_frame,
            text="🌙" if not self.is_dark_mode else "☀️",
            command=self.toggle_dark_mode,
            font=("Arial", 14),
            bg=self.colors['button_bg'],
            fg=self.colors['button_fg'],
            relief='flat',
            width=3
        )
        self.dark_mode_btn.pack(side='right', padx=10)
    
    def create_image_frame(self):
        """
        إنشاء إطار عرض الصورة
        """
        self.image_frame = tk.Frame(self.root, bg=self.colors['frame_bg'], relief='sunken', bd=2)
        self.image_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تسمية إطار الصورة
        image_label = tk.Label(
            self.image_frame,
            text="📷 صورة العين للتحليل",
            font=("Arial", 14, "bold"),
            bg=self.colors['frame_bg'],
            fg=self.colors['fg']
        )
        image_label.pack(pady=5)
        
        # منطقة عرض الصورة
        self.image_display = tk.Label(
            self.image_frame,
            text="اختر صورة للعين لبدء التحليل\n\nالصيغ المدعومة: JPG, PNG, BMP",
            font=("Arial", 12),
            bg=self.colors['frame_bg'],
            fg='gray',
            width=50,
            height=15,
            relief='dashed',
            bd=2
        )
        self.image_display.pack(pady=10, padx=20)
    
    def create_buttons_frame(self):
        """
        إنشاء إطار الأزرار
        """
        self.buttons_frame = tk.Frame(self.root, bg=self.colors['bg'])
        self.buttons_frame.pack(fill='x', padx=10, pady=5)
        
        # زر اختيار الصورة
        self.select_btn = tk.Button(
            self.buttons_frame,
            text="📁 اختيار صورة",
            command=self.select_image,
            font=("Arial", 12, "bold"),
            bg='#4CAF50',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        self.select_btn.pack(side='left', padx=5)
        
        # زر تحليل الصورة
        self.analyze_btn = tk.Button(
            self.buttons_frame,
            text="🔍 تحليل الصورة",
            command=self.analyze_image,
            font=("Arial", 12, "bold"),
            bg='#2196F3',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2,
            state='disabled'
        )
        self.analyze_btn.pack(side='left', padx=5)
        
        # زر سماع النتيجة
        self.speak_btn = tk.Button(
            self.buttons_frame,
            text="🔊 اسمع النتيجة",
            command=self.speak_result,
            font=("Arial", 12, "bold"),
            bg='#FF9800',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2,
            state='disabled'
        )
        self.speak_btn.pack(side='left', padx=5)
        
        # زر حفظ النتيجة
        self.save_btn = tk.Button(
            self.buttons_frame,
            text="💾 حفظ النتيجة",
            command=self.save_result,
            font=("Arial", 12, "bold"),
            bg='#9C27B0',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2,
            state='disabled'
        )
        self.save_btn.pack(side='left', padx=5)
        
        # زر المعلومات
        self.info_btn = tk.Button(
            self.buttons_frame,
            text="ℹ️ معلومات",
            command=self.show_info,
            font=("Arial", 12, "bold"),
            bg='#607D8B',
            fg='white',
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        self.info_btn.pack(side='right', padx=5)
    
    def create_results_frame(self):
        """
        إنشاء إطار النتائج
        """
        self.results_frame = tk.Frame(self.root, bg=self.colors['frame_bg'], relief='sunken', bd=2)
        self.results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تسمية إطار النتائج
        results_label = tk.Label(
            self.results_frame,
            text="📊 نتائج التحليل والتوصيات",
            font=("Arial", 14, "bold"),
            bg=self.colors['frame_bg'],
            fg=self.colors['fg']
        )
        results_label.pack(pady=5)
        
        # منطقة عرض النتائج
        self.results_text = scrolledtext.ScrolledText(
            self.results_frame,
            font=("Arial", 11),
            bg=self.colors['entry_bg'],
            fg=self.colors['entry_fg'],
            height=8,
            wrap='word',
            state='disabled'
        )
        self.results_text.pack(fill='both', expand=True, padx=10, pady=5)
    
    def create_status_frame(self):
        """
        إنشاء إطار الحالة
        """
        self.status_frame = tk.Frame(self.root, bg=self.colors['bg'], height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        # شريط الحالة
        self.status_label = tk.Label(
            self.status_frame,
            text="جاهز للاستخدام - اختر صورة للبدء",
            font=("Arial", 10),
            bg=self.colors['bg'],
            fg=self.colors['fg'],
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, fill='x', expand=True)
        
        # شريط التقدم
        self.progress = ttk.Progressbar(
            self.status_frame,
            mode='indeterminate',
            length=200
        )
        self.progress.pack(side='right', padx=10, pady=5)

    def setup_layout(self):
        """
        إعداد تخطيط الواجهة
        """
        # تحديث النص الترحيبي
        self.update_results_text("مرحباً بك في EyeGuard AI! 👋\n\nلبدء التحليل:\n1. اضغط على 'اختيار صورة'\n2. اختر صورة واضحة للعين\n3. اضغط على 'تحليل الصورة'\n\n⚠️ تنبيه: هذا التطبيق للاستخدام التعليمي فقط")

    def select_image(self):
        """
        اختيار صورة للتحليل
        """
        file_types = [
            ("صور", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG", "*.jpg *.jpeg"),
            ("PNG", "*.png"),
            ("BMP", "*.bmp"),
            ("جميع الملفات", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="اختر صورة العين",
            filetypes=file_types,
            initialdir=os.path.expanduser("~")
        )

        if file_path:
            if self.image_processor.is_valid_image(file_path):
                self.current_image_path = file_path
                self.display_image(file_path)
                self.analyze_btn.config(state='normal')
                self.update_status("تم اختيار الصورة - جاهز للتحليل")

                # عرض معلومات الصورة
                image_info = self.image_processor.get_image_info(file_path)
                if image_info:
                    info_text = f"معلومات الصورة:\n"
                    info_text += f"الأبعاد: {image_info['width']} × {image_info['height']}\n"
                    info_text += f"الصيغة: {image_info['format']}\n"
                    info_text += f"الحجم: {image_info['size_mb']:.2f} ميجابايت\n\n"
                    info_text += "اضغط على 'تحليل الصورة' للمتابعة"
                    self.update_results_text(info_text)
            else:
                messagebox.showerror("خطأ", "الملف المختار ليس صورة صحيحة")

    def display_image(self, image_path):
        """
        عرض الصورة في الواجهة
        """
        try:
            # فتح وتغيير حجم الصورة
            image = Image.open(image_path)

            # تحديد الحجم المناسب للعرض
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(image)

            # عرض الصورة
            self.image_display.config(image=photo, text="")
            self.image_display.image = photo  # الاحتفاظ بمرجع

        except Exception as e:
            print(f"خطأ في عرض الصورة: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض الصورة: {e}")

    def analyze_image(self):
        """
        تحليل الصورة باستخدام نموذج الذكاء الاصطناعي
        """
        if not self.current_image_path:
            messagebox.showwarning("تحذير", "يرجى اختيار صورة أولاً")
            return

        # بدء التحليل في خيط منفصل
        self.start_analysis()

    def start_analysis(self):
        """
        بدء عملية التحليل
        """
        # تعطيل الأزرار وبدء شريط التقدم
        self.analyze_btn.config(state='disabled')
        self.select_btn.config(state='disabled')
        self.progress.start()
        self.update_status("جاري التحليل...")

        # تشغيل التحليل في خيط منفصل
        analysis_thread = threading.Thread(target=self.perform_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def perform_analysis(self):
        """
        تنفيذ التحليل الفعلي
        """
        try:
            # تحليل الصورة
            prediction = self.model.predict(self.current_image_path)
            self.current_prediction = prediction

            # تحديث الواجهة في الخيط الرئيسي
            self.root.after(0, self.analysis_complete, prediction)

        except Exception as e:
            error_msg = f"خطأ في التحليل: {e}"
            self.root.after(0, self.analysis_error, error_msg)

    def analysis_complete(self, prediction):
        """
        إكمال التحليل وعرض النتائج
        """
        # إيقاف شريط التقدم
        self.progress.stop()

        # تفعيل الأزرار
        self.analyze_btn.config(state='normal')
        self.select_btn.config(state='normal')
        self.speak_btn.config(state='normal')
        self.save_btn.config(state='normal')

        # عرض النتائج
        self.display_results(prediction)

        # تحديث الحالة
        self.update_status(f"تم التحليل - النتيجة: {prediction['class_name']}")

    def analysis_error(self, error_msg):
        """
        التعامل مع خطأ في التحليل
        """
        # إيقاف شريط التقدم
        self.progress.stop()

        # تفعيل الأزرار
        self.analyze_btn.config(state='normal')
        self.select_btn.config(state='normal')

        # عرض رسالة الخطأ
        messagebox.showerror("خطأ في التحليل", error_msg)
        self.update_status("فشل في التحليل")

    def display_results(self, prediction):
        """
        عرض نتائج التحليل
        """
        class_name = prediction['class_name']
        confidence = prediction['confidence']

        # الحصول على معلومات العلاج
        treatment_info = self.treatment_guide.get_treatment_info(class_name)

        # تنسيق النص
        result_text = f"🎯 نتيجة التحليل:\n"
        result_text += f"التشخيص: {class_name}\n"
        result_text += f"مستوى الثقة: {confidence:.1%}\n\n"

        result_text += self.treatment_guide.format_treatment_text(class_name)

        result_text += "\n\n" + "="*50 + "\n"
        result_text += "⚠️ تنبيه طبي مهم:\n"
        result_text += "هذا التطبيق مخصص للاستخدام التعليمي والتوعوي فقط.\n"
        result_text += "النتائج المعروضة لا تغني عن استشارة طبيب مختص.\n"
        result_text += "في حالة وجود أي مشاكل في العين، يُنصح بزيارة طبيب العيون فوراً."

        self.update_results_text(result_text)

    def speak_result(self):
        """
        قراءة النتيجة صوتياً
        """
        if self.current_prediction:
            self.audio_handler.speak_diagnosis(self.current_prediction)
        else:
            messagebox.showwarning("تحذير", "لا توجد نتائج للقراءة")

    def save_result(self):
        """
        حفظ النتيجة كصورة
        """
        if not self.current_prediction or not self.current_image_path:
            messagebox.showwarning("تحذير", "لا توجد نتائج للحفظ")
            return

        # اختيار مكان الحفظ
        file_path = filedialog.asksaveasfilename(
            title="حفظ النتيجة",
            defaultextension=".png",
            filetypes=[("PNG", "*.png"), ("JPEG", "*.jpg"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            success = self.image_processor.create_result_image(
                self.current_image_path,
                self.current_prediction,
                file_path
            )

            if success:
                messagebox.showinfo("نجح", f"تم حفظ النتيجة في:\n{file_path}")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ النتيجة")

    def show_info(self):
        """
        عرض معلومات التطبيق
        """
        info_window = tk.Toplevel(self.root)
        info_window.title("معلومات التطبيق")
        info_window.geometry("600x500")
        info_window.configure(bg=self.colors['bg'])

        # النص المعلوماتي
        info_text = """
👁️ EyeGuard AI - حماية عيونك بالذكاء الاصطناعي

🎯 الهدف:
تطبيق ذكي مجاني لاكتشاف المشاكل الشائعة في العين وتقديم نصائح علاجية مبدئية.

✨ الميزات:
• تحليل ذكي للصور باستخدام الذكاء الاصطناعي
• اكتشاف 5 حالات مختلفة للعين
• نصائح علاجية مفصلة لكل حالة
• تحويل النتائج إلى كلام
• حفظ النتائج كصورة
• واجهة عربية سهلة الاستخدام

🔍 الحالات المدعومة:
• العين الطبيعية
• الاحمرار والالتهاب
• المياه البيضاء
• مشاكل الشبكية
• جفاف العين

⚠️ تنبيه مهم:
هذا التطبيق للاستخدام التعليمي فقط ولا يغني عن استشارة طبيب مختص.

🛠️ التقنيات المستخدمة:
• Python & PyTorch للذكاء الاصطناعي
• Tkinter لواجهة المستخدم
• PIL لمعالجة الصور
• pyttsx3 لتحويل النص لصوت

📧 للدعم والاستفسارات:
تواصل معنا عبر GitHub أو البريد الإلكتروني

© 2024 EyeGuard AI - مفتوح المصدر
        """

        # عرض النص
        text_widget = scrolledtext.ScrolledText(
            info_window,
            font=("Arial", 11),
            bg=self.colors['entry_bg'],
            fg=self.colors['entry_fg'],
            wrap='word'
        )
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', info_text)
        text_widget.config(state='disabled')

    def toggle_dark_mode(self):
        """
        تبديل الوضع المظلم
        """
        self.is_dark_mode = not self.is_dark_mode
        self.setup_theme()
        self.refresh_theme()

        # تحديث نص الزر
        self.dark_mode_btn.config(text="☀️" if self.is_dark_mode else "🌙")

    def refresh_theme(self):
        """
        تحديث ثيم جميع العناصر
        """
        # تحديث النافذة الرئيسية
        self.root.configure(bg=self.colors['bg'])

        # تحديث الإطارات
        for frame in [self.header_frame, self.buttons_frame, self.status_frame]:
            frame.configure(bg=self.colors['bg'])

        for frame in [self.image_frame, self.results_frame]:
            frame.configure(bg=self.colors['frame_bg'])

        # تحديث التسميات
        for widget in self.root.winfo_children():
            self.update_widget_theme(widget)

    def update_widget_theme(self, widget):
        """
        تحديث ثيم عنصر واحد
        """
        try:
            if isinstance(widget, tk.Label):
                widget.configure(bg=self.colors['bg'], fg=self.colors['fg'])
            elif isinstance(widget, tk.Frame):
                widget.configure(bg=self.colors['bg'])
                for child in widget.winfo_children():
                    self.update_widget_theme(child)
        except:
            pass

    def update_results_text(self, text):
        """
        تحديث نص النتائج
        """
        self.results_text.config(state='normal')
        self.results_text.delete('1.0', tk.END)
        self.results_text.insert('1.0', text)
        self.results_text.config(state='disabled')

    def update_status(self, message):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)

    def run(self):
        """
        تشغيل التطبيق
        """
        # رسالة ترحيب صوتية (اختيارية)
        try:
            self.audio_handler.speak_welcome()
        except:
            pass

        # تشغيل الحلقة الرئيسية
        self.root.mainloop()

if __name__ == "__main__":
    app = EyeGuardMainWindow()
    app.run()
